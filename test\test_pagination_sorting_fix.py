#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分页和排序修复验证测试

测试修复后的分页和排序功能是否正常工作：
1. 分页数据不重复
2. 排序功能正常
3. 分页组件状态正确
4. 新架构统一处理
"""

import sys
import os
import time
import logging
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from src.utils.log_config import setup_logger
from src.gui.prototype.prototype_main_window import PrototypeMainWindow


class PaginationSortingTest:
    """分页和排序修复验证测试"""
    
    def __init__(self):
        self.logger = setup_logger("PaginationSortingTest")
        self.app = None
        self.main_window = None
        self.test_results = []
        
    def setup_test_environment(self):
        """设置测试环境"""
        try:
            self.app = QApplication(sys.argv)
            self.main_window = PrototypeMainWindow()
            self.main_window.show()
            
            self.logger.info("✅ 测试环境设置完成")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 测试环境设置失败: {e}")
            return False
    
    def test_pagination_data_uniqueness(self):
        """测试分页数据唯一性"""
        try:
            self.logger.info("🔍 测试分页数据唯一性...")
            
            # 模拟加载数据
            table_name = "salary_data_2025_07_active_employees"
            
            # 检查表格数据服务是否可用
            if not hasattr(self.main_window, 'table_data_service') or not self.main_window.table_data_service:
                self.logger.warning("⚠️ 表格数据服务不可用，跳过此测试")
                return False
            
            # 获取第1页数据
            response1 = self.main_window.table_data_service.load_table_data(table_name, 1)
            if not response1 or not response1.success:
                self.logger.warning("⚠️ 无法加载第1页数据，跳过此测试")
                return False
            
            # 获取第2页数据
            response2 = self.main_window.table_data_service.load_table_data(table_name, 2)
            if not response2 or not response2.success:
                self.logger.warning("⚠️ 无法加载第2页数据，跳过此测试")
                return False
            
            # 检查数据是否不同
            page1_data = response1.data
            page2_data = response2.data
            
            if page1_data.empty or page2_data.empty:
                self.logger.warning("⚠️ 分页数据为空，跳过此测试")
                return False
            
            # 比较工号字段
            if '工号' in page1_data.columns and '工号' in page2_data.columns:
                page1_ids = set(page1_data['工号'].tolist())
                page2_ids = set(page2_data['工号'].tolist())
                
                # 检查是否有重复
                overlap = page1_ids.intersection(page2_ids)
                if len(overlap) == 0:
                    self.logger.info("✅ 分页数据唯一性测试通过：第1页和第2页数据不重复")
                    return True
                else:
                    self.logger.error(f"❌ 分页数据重复：{len(overlap)}个重复工号")
                    return False
            else:
                self.logger.warning("⚠️ 数据中没有工号字段，无法验证唯一性")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 分页数据唯一性测试失败: {e}")
            return False
    
    def test_pagination_total_records(self):
        """测试分页组件总记录数显示"""
        try:
            self.logger.info("🔍 测试分页组件总记录数...")
            
            # 检查分页组件
            if not hasattr(self.main_window, 'main_workspace') or not self.main_window.main_workspace:
                self.logger.warning("⚠️ 主工作区不可用，跳过此测试")
                return False
            
            pagination_widget = getattr(self.main_workspace, 'pagination_widget', None)
            if not pagination_widget:
                self.logger.warning("⚠️ 分页组件不可用，跳过此测试")
                return False
            
            # 检查总记录数
            total_records = pagination_widget.state.total_records
            if total_records > 100:  # 应该是1396而不是50
                self.logger.info(f"✅ 分页组件总记录数正确: {total_records}")
                return True
            else:
                self.logger.error(f"❌ 分页组件总记录数错误: {total_records} (应该 > 100)")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 分页组件总记录数测试失败: {e}")
            return False
    
    def test_sorting_field_mapping(self):
        """测试排序字段映射"""
        try:
            self.logger.info("🔍 测试排序字段映射...")
            
            # 检查表格组件
            if not hasattr(self.main_window, 'main_workspace') or not self.main_window.main_workspace:
                self.logger.warning("⚠️ 主工作区不可用，跳过此测试")
                return False
            
            expandable_table = getattr(self.main_workspace, 'expandable_table', None)
            if not expandable_table:
                self.logger.warning("⚠️ 表格组件不可用，跳过此测试")
                return False
            
            # 检查排序管理器
            sort_manager = getattr(expandable_table, 'sort_manager', None)
            if not sort_manager:
                self.logger.warning("⚠️ 排序管理器不可用，跳过此测试")
                return False
            
            # 检查字段映射
            field_mapping = getattr(sort_manager, 'field_mapping', {})
            if len(field_mapping) > 0:
                self.logger.info(f"✅ 排序字段映射正常: {len(field_mapping)}个映射")
                # 检查关键字段映射
                if '工号' in field_mapping:
                    db_field = field_mapping['工号']
                    self.logger.info(f"✅ 工号字段映射: '工号' -> '{db_field}'")
                return True
            else:
                self.logger.error("❌ 排序字段映射为空")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 排序字段映射测试失败: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        self.logger.info("🚀 开始分页和排序修复验证测试...")
        
        if not self.setup_test_environment():
            return False
        
        # 等待系统初始化
        time.sleep(2)
        
        tests = [
            ("分页数据唯一性", self.test_pagination_data_uniqueness),
            ("分页组件总记录数", self.test_pagination_total_records),
            ("排序字段映射", self.test_sorting_field_mapping),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            self.logger.info(f"\n--- 执行测试: {test_name} ---")
            try:
                if test_func():
                    passed += 1
                    self.test_results.append(f"✅ {test_name}: 通过")
                else:
                    self.test_results.append(f"❌ {test_name}: 失败")
            except Exception as e:
                self.logger.error(f"测试异常: {e}")
                self.test_results.append(f"💥 {test_name}: 异常 - {e}")
        
        # 输出测试结果
        self.logger.info("\n" + "="*50)
        self.logger.info("📊 测试结果汇总:")
        for result in self.test_results:
            self.logger.info(result)
        
        self.logger.info(f"\n🎯 总体结果: {passed}/{total} 个测试通过")
        
        if passed == total:
            self.logger.info("🎉 所有测试通过！分页和排序修复成功！")
        else:
            self.logger.warning(f"⚠️ {total - passed} 个测试失败，需要进一步检查")
        
        return passed == total


def main():
    """主函数"""
    test = PaginationSortingTest()
    success = test.run_all_tests()
    
    if success:
        print("\n🎉 分页和排序修复验证成功！")
        return 0
    else:
        print("\n❌ 分页和排序修复验证失败！")
        return 1


if __name__ == "__main__":
    sys.exit(main())
