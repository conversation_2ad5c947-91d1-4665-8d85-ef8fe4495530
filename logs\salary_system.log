2025-07-21 11:58:38.085 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-07-21 11:58:38.086 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-07-21 11:58:38.087 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-07-21 11:58:38.087 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-07-21 11:58:38.088 | INFO     | src.utils.log_config:_log_initialization_info:214 | 日志文件路径: logs/salary_system.log
2025-07-21 11:58:38.089 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-07-21 11:58:38.847 | INFO     | __main__:setup_qt_exception_handling:218 | 🔧 [P0-修复] PyQt专用异常处理机制已启用
2025-07-21 11:58:38.848 | INFO     | __main__:setup_app_logging:331 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-07-21 11:58:38.848 | INFO     | __main__:main:395 | 初始化核心管理器...
2025-07-21 11:58:38.854 | INFO     | src.modules.system_config.config_manager:__init__:313 | 配置管理器初始化完成，配置文件: config.json
2025-07-21 11:58:38.855 | INFO     | src.modules.system_config.config_manager:load_config:340 | 正在加载配置文件: config.json
2025-07-21 11:58:38.856 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-07-21 11:58:38.857 | INFO     | src.modules.system_config.config_manager:load_config:352 | 配置文件加载成功
2025-07-21 11:58:38.858 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-07-21 11:58:38.865 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-07-21 11:58:38.866 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: E:\project\case\salary_changes\salary_changes\data\db\salary_system.db
2025-07-21 11:58:38.867 | INFO     | src.modules.system_config.config_manager:__init__:313 | 配置管理器初始化完成，配置文件: config.json
2025-07-21 11:58:38.868 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:104 | 动态表管理器初始化完成
2025-07-21 11:58:38.869 | INFO     | __main__:main:400 | 核心管理器初始化完成。
2025-07-21 11:58:38.870 | INFO     | src.core.data_source_unification_manager:__init__:128 | 数据源统一管理器初始化完成
2025-07-21 11:58:38.871 | INFO     | src.core.table_sort_state_manager:__init__:174 | 表级排序状态管理器初始化完成
2025-07-21 11:58:38.886 | INFO     | src.core.architecture_factory:__init__:62 | 架构重构工厂初始化完成
2025-07-21 11:58:38.887 | INFO     | src.core.architecture_factory:initialize_architecture:72 | 开始初始化架构重构系统...
2025-07-21 11:58:38.993 | WARNING  | src.modules.data_import.config_sync_manager:_do_config_initialization:139 | 创建了最小默认配置，可能需要重新导入数据以生成字段映射
2025-07-21 11:58:38.997 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-07-21 11:58:38.999 | INFO     | src.core.event_bus:__init__:227 | 事件总线初始化完成
2025-07-21 11:58:39.001 | INFO     | src.core.unified_state_manager:_load_state:465 | 状态文件不存在，使用默认状态
2025-07-21 11:58:39.005 | INFO     | src.core.unified_state_manager:__init__:177 | 统一状态管理器初始化完成
2025-07-21 11:58:39.007 | INFO     | src.core.unified_data_request_manager:__init__:187 | 统一数据请求管理器初始化完成
2025-07-21 11:58:39.009 | INFO     | src.core.unified_data_request_manager:__init__:187 | 统一数据请求管理器初始化完成
2025-07-21 11:58:39.011 | INFO     | src.core.unified_state_manager:_load_state:465 | 状态文件不存在，使用默认状态
2025-07-21 11:58:39.015 | INFO     | src.core.unified_state_manager:__init__:177 | 统一状态管理器初始化完成
2025-07-21 11:58:39.018 | INFO     | src.services.table_data_service:__init__:73 | 表格数据服务初始化完成
2025-07-21 11:58:39.019 | INFO     | src.core.architecture_factory:initialize_architecture:103 | 🎉 架构重构系统初始化成功！耗时: 131.3ms
2025-07-21 11:58:39.035 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:46 | 数据预加载缓存初始化完成 - 最大条目数: 100, TTL: 300秒
2025-07-21 11:58:39.037 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:232 | 表格状态缓存初始化完成
2025-07-21 11:58:39.038 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:32 | 表头配置缓存初始化完成
2025-07-21 11:58:39.040 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:211 | 字段映射缓存初始化完成
2025-07-21 11:58:39.042 | INFO     | src.gui.prototype.performance.performance_manager:__init__:46 | 🚀 性能管理器初始化完成
2025-07-21 11:58:39.043 | INFO     | src.gui.prototype.prototype_main_window:__init__:2792 | 🚀 性能管理器已集成
2025-07-21 11:58:39.044 | INFO     | src.gui.prototype.prototype_main_window:__init__:2794 | ✅ 新架构集成成功！
2025-07-21 11:58:39.047 | INFO     | src.gui.prototype.prototype_main_window:_inject_config_sync_manager_to_existing_tables:2905 | ConfigSyncManager重新注入完成，已更新0个表格实例
2025-07-21 11:58:39.049 | INFO     | src.gui.prototype.prototype_main_window:_setup_new_architecture_listeners:2871 | ✅ 新架构事件监听器设置完成
2025-07-21 11:58:39.050 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-07-21 11:58:39.052 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-07-21 11:58:39.053 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-07-21 11:58:39.248 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:2074 | 菜单栏创建完成
2025-07-21 11:58:39.249 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: E:\project\case\salary_changes\salary_changes\user_preferences.json
2025-07-21 11:58:39.250 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: user_preferences.json
2025-07-21 11:58:39.252 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-07-21 11:58:39.254 | INFO     | src.gui.prototype.prototype_main_window:__init__:2050 | 菜单栏管理器初始化完成
2025-07-21 11:58:39.254 | INFO     | src.gui.table_header_manager:__init__:68 | 表头管理器初始化完成
2025-07-21 11:58:39.255 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:4019 | 管理器设置完成，包含增强版表头管理器
2025-07-21 11:58:39.258 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-07-21 11:58:39.262 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:148 | 智能树形展开算法初始化完成
2025-07-21 11:58:39.266 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:1042 | 开始从元数据动态加载工资数据...
2025-07-21 11:58:39.267 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:1049 | 检测到首次启动，将延迟加载导航数据确保数据库就绪
2025-07-21 11:58:39.269 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:680 | 导航面板已重构：移除功能性导航，专注数据导航
2025-07-21 11:58:39.271 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:715 | 恢复导航状态: 0个展开项
2025-07-21 11:58:39.272 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表', '工资表 > 2025年']
2025-07-21 11:58:39.273 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:521 | 增强导航面板初始化完成
2025-07-21 11:58:39.283 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1328 | 快捷键注册完成: 18/18 个
2025-07-21 11:58:39.284 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1673 | 拖拽排序管理器初始化完成
2025-07-21 11:58:39.285 | INFO     | src.modules.data_import.header_edit_manager:__init__:82 | 表头编辑管理器初始化完成
2025-07-21 11:58:39.286 | INFO     | src.core.unified_state_manager:_load_state:465 | 状态文件不存在，使用默认状态
2025-07-21 11:58:39.287 | INFO     | src.core.unified_state_manager:__init__:177 | 统一状态管理器初始化完成
2025-07-21 11:58:39.289 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2060 | 🧹 [代码清理] 格式管理器已迁移到MasterFormatManager
2025-07-21 11:58:39.303 | INFO     | src.gui.prototype.widgets.column_sort_manager:_load_field_mappings_from_config:337 | 🔧 [新架构] 成功加载 46 个字段映射
2025-07-21 11:58:39.304 | INFO     | src.gui.prototype.widgets.column_sort_manager:__init__:99 | 🆕 [新架构排序] 多列排序管理器初始化完成，最大排序列数: 3
2025-07-21 11:58:39.306 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2102 | 🆕 [新架构多列排序] 初始化完成，支持最多3列排序
2025-07-21 11:58:39.307 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1507 | 列宽管理器初始化完成
2025-07-21 11:58:39.309 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2109 | 列宽管理器初始化完成
2025-07-21 11:58:39.311 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2123 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-07-21 11:58:39.312 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3982 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-07-21 11:58:39.313 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 0 行, 7 列
2025-07-21 11:58:39.316 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 0 行, 耗时: 4.0ms
2025-07-21 11:58:39.318 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 11:58:39.320 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6315 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-07-21 11:58:39.321 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1578 | 🆕 [列宽保存] 配置文件不存在，使用默认列宽
2025-07-21 11:58:39.322 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1635 | 表格已初始化为空白状态，等待用户导入数据
2025-07-21 11:58:39.325 | INFO     | src.gui.widgets.pagination_widget:__init__:165 | 分页组件初始化完成
2025-07-21 11:58:39.336 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:517 | 控制面板按钮信号连接完成
2025-07-21 11:58:39.341 | INFO     | src.modules.system_config.config_manager:__init__:313 | 配置管理器初始化完成，配置文件: config.json
2025-07-21 11:58:39.342 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: E:\project\case\salary_changes\salary_changes\user_preferences.json
2025-07-21 11:58:39.343 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:3992 | 快捷键设置完成
2025-07-21 11:58:39.344 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:3981 | 主窗口UI设置完成。
2025-07-21 11:58:39.346 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:4030 | 🔧 [全局排序] 全局排序开关连接成功
2025-07-21 11:58:39.347 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:4058 | 🆕 [新架构排序] 使用表格组件内部的自定义排序循环，无需连接排序信号
2025-07-21 11:58:39.348 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:4067 | ✅ 已连接分页组件事件到新架构
2025-07-21 11:58:39.350 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:4069 | 信号连接设置完成
2025-07-21 11:58:39.351 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:4850 | 已加载字段映射信息，共0个表的映射
2025-07-21 11:58:39.353 | WARNING  | src.gui.prototype.prototype_main_window:set_data:645 | 尝试设置空数据，将显示提示信息。
2025-07-21 11:58:39.355 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3982 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-07-21 11:58:39.358 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 0 行, 7 列
2025-07-21 11:58:39.360 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 0 行, 耗时: 6.0ms
2025-07-21 11:58:39.361 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 11:58:39.362 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6315 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-07-21 11:58:39.363 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1578 | 🆕 [列宽保存] 配置文件不存在，使用默认列宽
2025-07-21 11:58:39.364 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1635 | 表格已初始化为空白状态，等待用户导入数据
2025-07-21 11:58:39.365 | INFO     | src.gui.widgets.pagination_widget:reset:354 | 分页状态已重置
2025-07-21 11:58:39.368 | WARNING  | src.gui.prototype.prototype_main_window:set_data:645 | 尝试设置空数据，将显示提示信息。
2025-07-21 11:58:39.369 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3982 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-07-21 11:58:39.371 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 0 行, 7 列
2025-07-21 11:58:39.372 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 0 行, 耗时: 3.0ms
2025-07-21 11:58:39.373 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 11:58:39.374 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6315 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-07-21 11:58:39.375 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1578 | 🆕 [列宽保存] 配置文件不存在，使用默认列宽
2025-07-21 11:58:39.378 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1635 | 表格已初始化为空白状态，等待用户导入数据
2025-07-21 11:58:39.379 | INFO     | src.gui.widgets.pagination_widget:reset:354 | 分页状态已重置
2025-07-21 11:58:39.380 | INFO     | src.gui.prototype.prototype_main_window:__init__:2845 | 原型主窗口初始化完成
2025-07-21 11:58:39.433 | INFO     | __main__:main:422 | 应用程序启动成功
2025-07-21 11:58:39.517 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1266px)
2025-07-21 11:58:39.519 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:1783 | MainWorkspaceArea 响应式适配: sm
2025-07-21 11:58:39.773 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1003 | 执行延迟的自动选择最新数据...
2025-07-21 11:58:39.776 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1014 | 导航树即将刷新，跳过当前自动选择，将在刷新后执行
2025-07-21 11:58:40.024 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1564 | 列宽设置已保存: default_table (7 列)
2025-07-21 11:58:40.070 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_load_salary_data:1064 | 执行延迟的工资数据加载...
2025-07-21 11:58:40.073 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1230 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-07-21 11:58:40.076 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1238 | 找到工资表节点: 📊 工资表
2025-07-21 11:58:40.083 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:885 | 找到 0 个匹配类型 'salary_data' 的表
2025-07-21 11:58:40.085 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:976 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-07-21 11:58:40.089 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:888 | 找到 3 个总表
2025-07-21 11:58:40.092 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1290 | 检测到数据库中没有工资数据表，直接使用兜底数据
2025-07-21 11:58:40.095 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_load_fallback_salary_data:1187 | 使用兜底数据加载导航
2025-07-21 11:58:40.299 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1093 | 导航树刷新完成，重新执行自动选择...
2025-07-21 11:58:40.302 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1036 | 开始获取最新工资数据路径...
2025-07-21 11:58:40.305 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:885 | 找到 0 个匹配类型 'salary_data' 的表
2025-07-21 11:58:40.307 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1041 | 未找到任何工资数据表
2025-07-21 11:58:40.312 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1098 | 未找到最新工资数据路径
2025-07-21 11:58:44.045 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:534 | 数据导入功能被触发，发出 import_requested 信号。
2025-07-21 11:58:44.047 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:4301 | 接收到数据导入请求，推断的目标路径: 工资表 > 2025年 > 07月 > 全部在职人员。打开导入对话框。
2025-07-21 11:58:44.048 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-07-21 11:58:44.050 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:72 | 多Sheet导入器初始化完成
2025-07-21 11:58:44.052 | INFO     | src.gui.widgets.target_selection_widget:_save_config:285 | 配置保存成功
2025-07-21 11:58:44.059 | INFO     | src.gui.widgets.target_selection_widget:set_target_from_path:505 | 从路径设置目标: 工资表 > 2025年 > 07月 > 全部在职人员
2025-07-21 11:58:44.061 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:885 | 找到 0 个匹配类型 'salary_data' 的表
2025-07-21 11:58:44.076 | INFO     | src.gui.main_dialogs:_get_template_fields:1872 | 🔧 [P3-修复] ConfigSyncManager未初始化，使用默认字段模板
2025-07-21 11:58:44.078 | INFO     | src.gui.main_dialogs:_init_field_mapping:1859 | 🔧 [P3-修复] 未找到字段模板，使用内置默认字段列表
2025-07-21 11:58:44.104 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-07-21 11:58:44.106 | INFO     | src.gui.main_dialogs:_apply_default_settings:2210 | 已应用默认设置: {'start_row': 1, 'import_mode': 'multi_sheet', 'auto_match_sheet': True, 'include_header': True, 'skip_empty_rows': True, 'create_table_mode': 'sheet_name', 'import_strategy': 'separate_tables', 'table_template': 'salary_data'}
2025-07-21 11:58:44.107 | INFO     | src.gui.main_dialogs:_setup_tooltips:2465 | 工具提示设置完成
2025-07-21 11:58:44.108 | INFO     | src.gui.main_dialogs:_setup_shortcuts:2504 | 快捷键设置完成
2025-07-21 11:58:44.108 | INFO     | src.gui.main_dialogs:__init__:77 | 数据导入对话框初始化完成。
2025-07-21 11:58:44.109 | INFO     | src.modules.data_import.multi_sheet_importer:set_config_sync_manager:81 | 🔧 [修复] ConfigSyncManager已设置到MultiSheetImporter
2025-07-21 11:58:44.110 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:4312 | ConfigSyncManager已设置到数据导入对话框
2025-07-21 11:58:44.802 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1564 | 列宽设置已保存: default_table (7 列)
2025-07-21 11:58:50.885 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-21 11:58:52.254 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-21 11:58:52.256 | INFO     | src.modules.data_import.smart_sheet_matcher:record_user_choice:372 | 记录用户选择: 全部在职人员 -> 全部在职人员工资表
2025-07-21 11:58:52.257 | INFO     | src.gui.main_dialogs:_auto_select_sheet_by_category:2245 | 根据人员类别 '全部在职人员' 自动选择工作表: 全部在职人员工资表 (匹配类型: fuzzy, 得分: 0.60)
2025-07-21 11:58:53.776 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-21 11:58:53.854 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-21 11:58:53.862 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-21 11:58:53.864 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:211 | 开始导入多Sheet Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-07-21 11:58:53.866 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-21 11:58:53.945 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-21 11:58:53.946 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:222 | 检测到 4 个工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-21 11:58:53.948 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-21 11:58:53.949 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-07-21 11:58:53.950 | INFO     | src.utils.log_config:log_file_operation:252 | 文件E:\project\case\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-07-21 11:58:53.993 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:320 | 🔧 [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-07-21 11:58:53.995 | INFO     | src.modules.data_import.excel_importer:_clean_data:427 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-07-21 11:58:53.997 | INFO     | src.modules.data_import.excel_importer:_clean_data:438 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-07-21 11:58:53.998 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:339 | 导入完成: 3行 x 16列
2025-07-21 11:58:54.001 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:567 | 数据质量检查: 已过滤1条姓名为空的记录
2025-07-21 11:58:54.002 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:608 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-07-21 11:58:54.005 | INFO     | src.modules.data_import.excel_importer:import_data:286 | 🔧 [修复标识] 数据导入最终完成: 2行 × 16列
2025-07-21 11:58:54.006 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:456 | 工作表 离休人员工资表 使用智能默认配置
2025-07-21 11:58:54.007 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:744 | 为Sheet '离休人员工资表' 生成智能默认配置: 1 个必需字段
2025-07-21 11:58:54.009 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:139 | 为模板 retired_employees 生成了 21 个字段映射
2025-07-21 11:58:54.009 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:473 | 使用专用模板 retired_employees 生成字段映射: 21 个字段
2025-07-21 11:58:54.012 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:615 | 完整字段映射保存成功: salary_data_2025_07_retired_employees
2025-07-21 11:58:54.014 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:520 | 为表 salary_data_2025_07_retired_employees 生成标准化字段映射: 21 个字段
2025-07-21 11:58:54.018 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:541 | Sheet 离休人员工资表 数据处理完成: 2 行
2025-07-21 11:58:54.019 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:371 | Sheet '离休人员工资表' 检测到模板类型: retired_employees
2025-07-21 11:58:54.021 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:700 | 成功创建表: salary_data_2025_07_retired_employees
2025-07-21 11:58:54.023 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:360 | 专用工资数据表创建成功: salary_data_2025_07_retired_employees (模板: retired_employees)
2025-07-21 11:58:54.058 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1135 | 🔧 [修复标识] 导入字段映射加载完成: 28 个映射规则
2025-07-21 11:58:54.060 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1143 | 🔧 [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-07-21 11:58:54.061 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1152 | 🔧 [修复标识] 导入列名映射成功: 18 个字段已映射
2025-07-21 11:58:54.066 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1289 | 成功向表 salary_data_2025_07_retired_employees 保存 2 条数据。
2025-07-21 11:58:54.068 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-21 11:58:54.069 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-07-21 11:58:54.070 | INFO     | src.utils.log_config:log_file_operation:252 | 文件E:\project\case\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-07-21 11:58:54.111 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:320 | 🔧 [修复标识] Excel读取完成: 27列 (列过滤: 否)
2025-07-21 11:58:54.113 | INFO     | src.modules.data_import.excel_importer:_clean_data:427 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-07-21 11:58:54.115 | INFO     | src.modules.data_import.excel_importer:_clean_data:438 | 数据清理完成: 保留所有 27 列 (原始 27 列)
2025-07-21 11:58:54.116 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:339 | 导入完成: 14行 x 27列
2025-07-21 11:58:54.118 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:567 | 数据质量检查: 已过滤1条姓名为空的记录
2025-07-21 11:58:54.119 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:608 | 数据过滤完成: 原始14条记录，过滤1条无效记录，有效记录13条
2025-07-21 11:58:54.123 | INFO     | src.modules.data_import.excel_importer:import_data:286 | 🔧 [修复标识] 数据导入最终完成: 13行 × 27列
2025-07-21 11:58:54.124 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:456 | 工作表 退休人员工资表 使用智能默认配置
2025-07-21 11:58:54.125 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:744 | 为Sheet '退休人员工资表' 生成智能默认配置: 1 个必需字段
2025-07-21 11:58:54.126 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:139 | 为模板 pension_employees 生成了 32 个字段映射
2025-07-21 11:58:54.127 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:473 | 使用专用模板 pension_employees 生成字段映射: 32 个字段
2025-07-21 11:58:54.129 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:615 | 完整字段映射保存成功: salary_data_2025_07_pension_employees
2025-07-21 11:58:54.130 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:520 | 为表 salary_data_2025_07_pension_employees 生成标准化字段映射: 32 个字段
2025-07-21 11:58:54.135 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:541 | Sheet 退休人员工资表 数据处理完成: 13 行
2025-07-21 11:58:54.136 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:371 | Sheet '退休人员工资表' 检测到模板类型: pension_employees
2025-07-21 11:58:54.138 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:700 | 成功创建表: salary_data_2025_07_pension_employees
2025-07-21 11:58:54.140 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:360 | 专用工资数据表创建成功: salary_data_2025_07_pension_employees (模板: pension_employees)
2025-07-21 11:58:54.154 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1135 | 🔧 [修复标识] 导入字段映射加载完成: 32 个映射规则
2025-07-21 11:58:54.155 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1143 | 🔧 [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-07-21 11:58:54.156 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1152 | 🔧 [修复标识] 导入列名映射成功: 29 个字段已映射
2025-07-21 11:58:54.161 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1289 | 成功向表 salary_data_2025_07_pension_employees 保存 13 条数据。
2025-07-21 11:58:54.163 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-21 11:58:54.164 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-07-21 11:58:54.165 | INFO     | src.utils.log_config:log_file_operation:252 | 文件E:\project\case\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-07-21 11:58:54.214 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:320 | 🔧 [修复标识] Excel读取完成: 23列 (列过滤: 否)
2025-07-21 11:58:54.216 | INFO     | src.modules.data_import.excel_importer:_clean_data:427 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-07-21 11:58:54.218 | INFO     | src.modules.data_import.excel_importer:_clean_data:438 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-07-21 11:58:54.219 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:339 | 导入完成: 1397行 x 23列
2025-07-21 11:58:54.222 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:567 | 数据质量检查: 已过滤1条姓名为空的记录
2025-07-21 11:58:54.224 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:608 | 数据过滤完成: 原始1397条记录，过滤1条无效记录，有效记录1396条
2025-07-21 11:58:54.226 | INFO     | src.modules.data_import.excel_importer:import_data:286 | 🔧 [修复标识] 数据导入最终完成: 1396行 × 23列
2025-07-21 11:58:54.228 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:456 | 工作表 全部在职人员工资表 使用智能默认配置
2025-07-21 11:58:54.229 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:744 | 为Sheet '全部在职人员工资表' 生成智能默认配置: 2 个必需字段
2025-07-21 11:58:54.230 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:139 | 为模板 active_employees 生成了 28 个字段映射
2025-07-21 11:58:54.231 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:473 | 使用专用模板 active_employees 生成字段映射: 28 个字段
2025-07-21 11:58:54.233 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:615 | 完整字段映射保存成功: salary_data_2025_07_active_employees
2025-07-21 11:58:54.236 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:520 | 为表 salary_data_2025_07_active_employees 生成标准化字段映射: 28 个字段
2025-07-21 11:58:54.240 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:541 | Sheet 全部在职人员工资表 数据处理完成: 1396 行
2025-07-21 11:58:54.241 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:371 | Sheet '全部在职人员工资表' 检测到模板类型: active_employees
2025-07-21 11:58:54.243 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:700 | 成功创建表: salary_data_2025_07_active_employees
2025-07-21 11:58:54.246 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:360 | 专用工资数据表创建成功: salary_data_2025_07_active_employees (模板: active_employees)
2025-07-21 11:58:54.258 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1135 | 🔧 [修复标识] 导入字段映射加载完成: 28 个映射规则
2025-07-21 11:58:54.260 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1143 | 🔧 [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-07-21 11:58:54.261 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1152 | 🔧 [修复标识] 导入列名映射成功: 25 个字段已映射
2025-07-21 11:58:54.272 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1289 | 成功向表 salary_data_2025_07_active_employees 保存 1396 条数据。
2025-07-21 11:58:54.273 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-21 11:58:54.275 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-07-21 11:58:54.276 | INFO     | src.utils.log_config:log_file_operation:252 | 文件E:\project\case\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-07-21 11:58:54.317 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:320 | 🔧 [修复标识] Excel读取完成: 21列 (列过滤: 否)
2025-07-21 11:58:54.319 | INFO     | src.modules.data_import.excel_importer:_clean_data:427 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-07-21 11:58:54.320 | INFO     | src.modules.data_import.excel_importer:_clean_data:438 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-07-21 11:58:54.321 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:339 | 导入完成: 63行 x 21列
2025-07-21 11:58:54.323 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:567 | 数据质量检查: 已过滤1条姓名为空的记录
2025-07-21 11:58:54.325 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:608 | 数据过滤完成: 原始63条记录，过滤1条无效记录，有效记录62条
2025-07-21 11:58:54.328 | INFO     | src.modules.data_import.excel_importer:import_data:286 | 🔧 [修复标识] 数据导入最终完成: 62行 × 21列
2025-07-21 11:58:54.329 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:456 | 工作表 A岗职工 使用智能默认配置
2025-07-21 11:58:54.330 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:744 | 为Sheet 'A岗职工' 生成智能默认配置: 2 个必需字段
2025-07-21 11:58:54.331 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:139 | 为模板 a_grade_employees 生成了 26 个字段映射
2025-07-21 11:58:54.332 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:473 | 使用专用模板 a_grade_employees 生成字段映射: 26 个字段
2025-07-21 11:58:54.335 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:615 | 完整字段映射保存成功: salary_data_2025_07_a_grade_employees
2025-07-21 11:58:54.338 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:520 | 为表 salary_data_2025_07_a_grade_employees 生成标准化字段映射: 26 个字段
2025-07-21 11:58:54.341 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:541 | Sheet A岗职工 数据处理完成: 62 行
2025-07-21 11:58:54.342 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:371 | Sheet 'A岗职工' 检测到模板类型: a_grade_employees
2025-07-21 11:58:54.344 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:700 | 成功创建表: salary_data_2025_07_a_grade_employees
2025-07-21 11:58:54.346 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:360 | 专用工资数据表创建成功: salary_data_2025_07_a_grade_employees (模板: a_grade_employees)
2025-07-21 11:58:54.359 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1135 | 🔧 [修复标识] 导入字段映射加载完成: 26 个映射规则
2025-07-21 11:58:54.361 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1143 | 🔧 [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-07-21 11:58:54.362 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1152 | 🔧 [修复标识] 导入列名映射成功: 23 个字段已映射
2025-07-21 11:58:54.367 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1289 | 成功向表 salary_data_2025_07_a_grade_employees 保存 62 条数据。
2025-07-21 11:58:54.368 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:241 | 多Sheet导入完成: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2025_07_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2025_07_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2025_07_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2025_07_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2025_07_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'file_info': {'path': 'E:\\project\\case\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']}
2025-07-21 11:58:54.370 | INFO     | src.gui.main_dialogs:_execute_multi_sheet_import:1479 | 多Sheet导入成功: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2025_07_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2025_07_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2025_07_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2025_07_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2025_07_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'file_info': {'path': 'E:\\project\\case\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2025-07', 'data_description': '2025年7月工资数据', 'file_path': 'E:/project/case/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2025年 > 07月 > 全部在职人员'}
2025-07-21 11:58:54.373 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:4322 | 开始处理导入结果: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2025_07_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2025_07_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2025_07_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2025_07_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2025_07_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'file_info': {'path': 'E:\\project\\case\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2025-07', 'data_description': '2025年7月工资数据', 'file_path': 'E:/project/case/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2025年 > 07月 > 全部在职人员'}
2025-07-21 11:58:54.375 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:4333 | 导入模式: multi_sheet, 目标路径: '工资表 > 2025年 > 07月 > 全部在职人员'
2025-07-21 11:58:54.379 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:4351 | 接收到导入数据, 来源: 未知来源, 目标路径: 工资表 > 2025年 > 07月 > 全部在职人员
2025-07-21 11:58:55.181 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:4439 | 检查是否需要更新导航面板: ['工资表', '2025年', '07月', '全部在职人员']
2025-07-21 11:58:55.184 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:4443 | 检测到工资数据导入，开始刷新导航面板
2025-07-21 11:58:55.187 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:4447 | 使用强制刷新方法
2025-07-21 11:58:55.190 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1230 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-07-21 11:58:55.194 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1238 | 找到工资表节点: 📊 工资表
2025-07-21 11:58:55.202 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:885 | 找到 4 个匹配类型 'salary_data' 的表
2025-07-21 11:58:55.206 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2025年，包含 1 个月份
2025-07-21 11:58:55.209 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1343 | 工资数据导航已强制刷新: 1 个年份, 1 个月份
2025-07-21 11:58:55.211 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1346 | 默认路径: 工资表 > 2025年 > 7月 > 全部在职人员
2025-07-21 11:58:55.216 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1385 | force_refresh_salary_data 执行完成
2025-07-21 11:58:55.219 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:4452 | 将在1500ms后导航到: 工资表 > 2025年 > 07月 > 全部在职人员
2025-07-21 11:58:56.709 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:4528 | 尝试导航到新导入的路径: 工资表 > 2025年 > 07月 > 全部在职人员
2025-07-21 11:58:56.712 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:4533 | 已成功导航到新导入的路径: 工资表 > 2025年 > 07月 > 全部在职人员
2025-07-21 11:58:57.210 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:5800 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '07月', '全部在职人员'] -> salary_data_2025_07_active_employees
2025-07-21 11:58:57.213 | INFO     | src.gui.prototype.prototype_main_window:_refresh_current_data_display:4621 | 开始刷新当前数据显示: salary_data_2025_07_active_employees
2025-07-21 11:58:57.216 | INFO     | src.gui.prototype.prototype_main_window:_refresh_current_data_display:4628 | 分页模式刷新: 第1页，每页50条
2025-07-21 11:58:57.218 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:5363 | 使用分页模式加载 salary_data_2025_07_active_employees，第1页，每页50条
2025-07-21 11:58:57.224 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:5452 | 缓存未命中，从数据库加载: salary_data_2025_07_active_employees 第1页
2025-07-21 11:58:57.227 | INFO     | src.gui.prototype.prototype_main_window:run:129 | 开始加载表 salary_data_2025_07_active_employees 第1页数据，每页50条
2025-07-21 11:58:57.244 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_07_active_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=0列
2025-07-21 11:58:57.247 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | 🔧 [P0-CRITICAL修复] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_07_active_employees" LIMIT 50 OFFSET 0
2025-07-21 11:58:57.254 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:557 | 🔧 [P0-CRITICAL修复] 数据库查询返回的前5个工号: ['19990089.0', '20161565.0', '20191782.0', '20151515.0', '20181640.0']
2025-07-21 11:58:57.259 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:569 | 成功从表 salary_data_2025_07_active_employees 获取第1页数据（含排序）: 50 行，总计1396行
2025-07-21 11:58:57.262 | INFO     | src.gui.prototype.prototype_main_window:run:155 | 使用排序查询: 0 个排序列
2025-07-21 11:58:57.264 | INFO     | src.gui.prototype.prototype_main_window:run:186 | 原始数据: 50行, 28列
2025-07-21 11:58:57.269 | INFO     | src.gui.prototype.prototype_main_window:run:193 | 开始应用字段映射
2025-07-21 11:58:57.271 | INFO     | src.gui.prototype.prototype_main_window:_apply_unified_field_processing:4950 | 开始统一字段处理: salary_data_2025_07_active_employees, 原始列数: 28
2025-07-21 11:58:57.282 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5147 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 11:58:57.286 | INFO     | src.gui.prototype.prototype_main_window:_apply_unified_field_processing:4999 | 🔧 [字段处理] 统一字段处理完成并缓存: 24个字段
2025-07-21 11:58:57.291 | INFO     | src.gui.prototype.prototype_main_window:run:203 | PaginationWorker - 字段映射成功: 28 -> 24列
2025-07-21 11:58:57.294 | INFO     | src.gui.prototype.prototype_main_window:run:217 | 字段映射成功: 24列
2025-07-21 11:58:57.296 | INFO     | src.gui.prototype.prototype_main_window:run:243 | 最终数据: 50行, 24列, 总记录数: 1396
2025-07-21 11:58:57.301 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:5483 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-07-21 11:58:57.304 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:5517 | 🚀 [性能缓存] 数据已缓存: salary_data_2025_07_active_employees 第1页
2025-07-21 11:58:57.312 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:439 | 正在从表 salary_data_2025_07_active_employees 分页获取数据: 第2页, 每页50条
2025-07-21 11:58:57.313 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:5546 | 🧹 [异步分页] 使用重构后的统一格式化结果: 50行, 24列
2025-07-21 11:58:57.320 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:462 | 成功从表 salary_data_2025_07_active_employees 获取第2页数据: 50 行，总计1396行
2025-07-21 11:58:57.320 | INFO     | src.gui.prototype.prototype_main_window:set_data:685 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-21 11:58:57.333 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5147 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 11:58:57.336 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 1000 -> 50
2025-07-21 11:58:57.337 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 11:58:57.363 | WARNING  | src.modules.format_management.master_format_manager:_load_format_config:319 | 配置文件不存在: state/data/format_config.json
2025-07-21 11:58:57.364 | INFO     | src.modules.format_management.master_format_manager:__init__:148 | 🎯 [统一格式化] 主格式化管理器已初始化
2025-07-21 11:58:57.372 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:238 | 开始格式化工资表数据 - 表类型: active_employees
2025-07-21 11:58:57.373 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:251 | 原始数据: 50行, 24列
2025-07-21 11:58:57.374 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:255 | 🔧 [表名映射] 输入表名: active_employees, 提取的表类型: active_employees
2025-07-21 11:58:57.376 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:259 | 使用表类型配置: active_employees, 配置字段: ['id_fields', 'string_fields', 'integer_fields', 'float_fields', 'special_fields']
2025-07-21 11:58:57.377 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:263 | 🔧 [格式化调试] 开始处理ID字段: ['employee_id', 'employee_type_code', '工号', '人员类别代码']
2025-07-21 11:58:57.378 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 工号 字段样例: ['19990089.0', '20161565.0', '20191782.0']
2025-07-21 11:58:57.379 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 人员类别代码 字段样例: ['1.0', '1.0', '17.0']
2025-07-21 11:58:57.380 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 工号: 原始样例=['19990089.0', '20161565.0', '20191782.0'], 原始类型=object
2025-07-21 11:58:57.383 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 工号: 样例=['19990089', '20161565', '20191782']
2025-07-21 11:58:57.384 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 工号: 样例=['19990089', '20161565', '20191782']
2025-07-21 11:58:57.385 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 工号 格式化完成，无小数点问题
2025-07-21 11:58:57.386 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 人员类别代码: 原始样例=['1.0', '1.0', '17.0'], 原始类型=object
2025-07-21 11:58:57.388 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 人员类别代码: 样例=['1', '1', '17']
2025-07-21 11:58:57.389 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 人员类别代码: 样例=['01', '01', '17']
2025-07-21 11:58:57.390 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 人员类别代码 格式化完成，无小数点问题
2025-07-21 11:58:57.393 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 工号 字段样例: ['19990089', '20161565', '20191782']
2025-07-21 11:58:57.394 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 人员类别代码 字段样例: ['01', '01', '17']
2025-07-21 11:58:57.395 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:472 | 🔧 [强化调试] 开始格式化字符串字段: ['employee_id', 'employee_name', 'department', 'employee_type_code', 'employee_type', '工号', '姓名', '部门名称', '人员类别代码', '人员类别']
2025-07-21 11:58:57.397 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 工号: 原始样例=['19990089', '20161565', '20191782'], 原始类型=object
2025-07-21 11:58:57.398 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:504 | 🔧 [修复] 工号字段 工号 已特殊处理，去除小数点格式
2025-07-21 11:58:57.399 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 工号: 样例=['19990089', '20161565', '20191782'], 类型=object
2025-07-21 11:58:57.400 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 工号 格式化完成，无小数点问题
2025-07-21 11:58:57.403 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 姓名: 原始样例=['杨胜', '胡四平', '肖啸'], 原始类型=object
2025-07-21 11:58:57.405 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 姓名: 样例=['杨胜', '胡四平', '肖啸'], 类型=object
2025-07-21 11:58:57.406 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 姓名 格式化完成，无小数点问题
2025-07-21 11:58:57.407 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 部门名称: 原始样例=['自动化学院', '自动化学院', '自动化学院'], 原始类型=object
2025-07-21 11:58:57.409 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 部门名称: 样例=['自动化学院', '自动化学院', '自动化学院'], 类型=object
2025-07-21 11:58:57.410 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 部门名称 格式化完成，无小数点问题
2025-07-21 11:58:57.413 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 人员类别代码: 原始样例=['01', '01', '17'], 原始类型=object
2025-07-21 11:58:57.415 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 人员类别代码: 样例=['01', '01', '17'], 类型=object
2025-07-21 11:58:57.416 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 人员类别代码 格式化完成，无小数点问题
2025-07-21 11:58:57.417 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 人员类别: 原始样例=['教学院其它人员', '教学单位专技人员', '科研单位人员'], 原始类型=object
2025-07-21 11:58:57.419 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 人员类别: 样例=['教学院其它人员', '教学单位专技人员', '科研单位人员'], 类型=object
2025-07-21 11:58:57.420 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 人员类别 格式化完成，无小数点问题
2025-07-21 11:58:57.422 | INFO     | src.modules.data_storage.salary_data_formatter:_format_integer_fields:604 | 🔧 [强化调试] 开始格式化整型字段: []
2025-07-21 11:58:57.424 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:655 | 🔧 [强化调试] 开始格式化浮点型字段: ['position_salary_2025', 'grade_salary_2025', 'allowance', 'balance_allowance', 'basic_performance_2025', 'health_fee', 'transport_allowance', 'property_allowance', 'communication_allowance', 'performance_bonus_2025', 'provident_fund_2025', 'housing_allowance', 'car_allowance', 'supplement', 'advance', 'total_salary', 'pension_insurance', '2025年岗位工资', '2025年薪级工资', '津贴', '结余津贴', '2025年基础性绩效', '卫生费', '交通补贴', '物业补贴', '通讯补贴', '2025年奖励性绩效预发', '2025公积金', '住房补贴', '车补', '补发', '借支', '应发工资', '代扣代存养老保险']
2025-07-21 11:58:57.425 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年岗位工资: 原始样例=[2880.0, 3030.0, 2185.0], 原始类型=float64
2025-07-21 11:58:57.426 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年岗位工资: 样例=[2880.0, 3030.0, 2185.0], 类型=float64
2025-07-21 11:58:57.427 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年岗位工资 格式化完成，保留两位小数
2025-07-21 11:58:57.428 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年薪级工资: 原始样例=[2375.0, 1696.0, 1427.0], 原始类型=float64
2025-07-21 11:58:57.430 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年薪级工资: 样例=[2375.0, 1696.0, 1427.0], 类型=float64
2025-07-21 11:58:57.433 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年薪级工资 格式化完成，保留两位小数
2025-07-21 11:58:57.434 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 津贴: 原始样例=[102.0, 0.0, 0.0], 原始类型=float64
2025-07-21 11:58:57.435 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 津贴: 样例=[102.0, 0.0, 0.0], 类型=float64
2025-07-21 11:58:57.436 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 津贴 格式化完成，保留两位小数
2025-07-21 11:58:57.437 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 结余津贴: 原始样例=[56.0, 56.0, 56.0], 原始类型=float64
2025-07-21 11:58:57.439 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 结余津贴: 样例=[56.0, 56.0, 56.0], 类型=float64
2025-07-21 11:58:57.440 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 结余津贴 格式化完成，保留两位小数
2025-07-21 11:58:57.441 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年基础性绩效: 原始样例=[3594.0, 3466.0, 2978.0], 原始类型=float64
2025-07-21 11:58:57.444 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年基础性绩效: 样例=[3594.0, 3466.0, 2978.0], 类型=float64
2025-07-21 11:58:57.445 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年基础性绩效 格式化完成，保留两位小数
2025-07-21 11:58:57.447 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 卫生费: 原始样例=[0.0, 0.0, 0.0], 原始类型=float64
2025-07-21 11:58:57.448 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 卫生费: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-21 11:58:57.449 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 卫生费 格式化完成，保留两位小数
2025-07-21 11:58:57.450 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 交通补贴: 原始样例=[220.0, 220.0, 220.0], 原始类型=float64
2025-07-21 11:58:57.452 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 交通补贴: 样例=[220.0, 220.0, 220.0], 类型=float64
2025-07-21 11:58:57.454 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 交通补贴 格式化完成，保留两位小数
2025-07-21 11:58:57.456 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 物业补贴: 原始样例=[240.0, 240.0, 200.0], 原始类型=float64
2025-07-21 11:58:57.457 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 物业补贴: 样例=[240.0, 240.0, 200.0], 类型=float64
2025-07-21 11:58:57.458 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 物业补贴 格式化完成，保留两位小数
2025-07-21 11:58:57.459 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 通讯补贴: 原始样例=[50.0, nan, nan], 原始类型=float64
2025-07-21 11:58:57.461 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 通讯补贴: 样例=[50.0, 0.0, 0.0], 类型=float64
2025-07-21 11:58:57.462 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 通讯补贴 格式化完成，保留两位小数
2025-07-21 11:58:57.464 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年奖励性绩效预发: 原始样例=[2500.0, 1000.0, 1000.0], 原始类型=float64
2025-07-21 11:58:57.466 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年奖励性绩效预发: 样例=[2500.0, 1000.0, 1000.0], 类型=float64
2025-07-21 11:58:57.467 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年奖励性绩效预发 格式化完成，保留两位小数
2025-07-21 11:58:57.468 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025公积金: 原始样例=[2097.0, 1860.0, 1984.0], 原始类型=float64
2025-07-21 11:58:57.469 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025公积金: 样例=[2097.0, 1860.0, 1984.0], 类型=float64
2025-07-21 11:58:57.470 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025公积金 格式化完成，保留两位小数
2025-07-21 11:58:57.471 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 住房补贴: 原始样例=[271.9745083294993, 189.0, 174.16354166666667], 原始类型=float64
2025-07-21 11:58:57.474 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 住房补贴: 样例=[271.97, 189.0, 174.16], 类型=float64
2025-07-21 11:58:57.475 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 住房补贴 格式化完成，保留两位小数
2025-07-21 11:58:57.476 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 车补: 原始样例=[None, None, None], 原始类型=object
2025-07-21 11:58:57.478 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 车补: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-21 11:58:57.479 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 车补 格式化完成，保留两位小数
2025-07-21 11:58:57.480 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 补发: 原始样例=[None, None, None], 原始类型=object
2025-07-21 11:58:57.481 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 补发: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-21 11:58:57.484 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 补发 格式化完成，保留两位小数
2025-07-21 11:58:57.485 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 借支: 原始样例=[nan, nan, 2000.0], 原始类型=float64
2025-07-21 11:58:57.487 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 借支: 样例=[0.0, 0.0, 2000.0], 类型=float64
2025-07-21 11:58:57.488 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 借支 格式化完成，保留两位小数
2025-07-21 11:58:57.489 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 应发工资: 原始样例=[12288.9745083295, 9897.0, 6240.163541666667], 原始类型=float64
2025-07-21 11:58:57.490 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 应发工资: 样例=[12288.97, 9897.0, 6240.16], 类型=float64
2025-07-21 11:58:57.491 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 应发工资 格式化完成，保留两位小数
2025-07-21 11:58:57.494 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 代扣代存养老保险: 原始样例=[1525.8000000000002, 1140.53, 1113.75], 原始类型=float64
2025-07-21 11:58:57.496 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 代扣代存养老保险: 样例=[1525.8, 1140.53, 1113.75], 类型=float64
2025-07-21 11:58:57.497 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 代扣代存养老保险 格式化完成，保留两位小数
2025-07-21 11:58:57.498 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:711 | 🔧 [特殊字段] 开始处理表类型 active_employees 的月份、年份字段
2025-07-21 11:58:57.499 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:866 | 🔧 [特殊字段] 月份、年份字段处理完成
2025-07-21 11:58:57.501 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:291 | 数据格式化完成: 50行, 24列
2025-07-21 11:58:57.504 | INFO     | src.modules.format_management.master_format_manager:format_table_data:187 | 🎯 [统一格式化] 数据格式化完成: active_employees
2025-07-21 11:58:57.506 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:58:57.507 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 11:58:57.508 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 11:58:57.509 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 11:58:57.510 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 11:58:57.511 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 11:58:57.514 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 11:58:57.515 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 11:58:57.517 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 11:58:57.518 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 11:58:57.519 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 11:58:57.520 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 11:58:57.521 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 11:58:57.524 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 11:58:57.526 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 11:58:57.535 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:409 | 🔧 [新架构] 排序管理器切换到表格: salary_data_2025_07_active_employees
2025-07-21 11:58:57.537 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:440 | 🔧 [新架构] 为表格 salary_data_2025_07_active_employees 重新加载 28 个字段映射
2025-07-21 11:58:57.537 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 199.8ms
2025-07-21 11:58:57.539 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 11:58:57.540 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6315 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-07-21 11:58:57.548 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1590 | 🆕 [列宽保存] 未找到表格 salary_data_2025_07_active_employees 的列宽配置
2025-07-21 11:58:57.549 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 50
2025-07-21 11:58:57.550 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 11:59:03.768 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1564 | 列宽设置已保存: salary_data_2025_07_active_employees (24 列)
2025-07-21 11:59:05.675 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1564 | 列宽设置已保存: salary_data_2025_07_active_employees (24 列)
2025-07-21 11:59:06.533 | INFO     | src.gui.prototype.widgets.column_sort_manager:handle_header_click:132 | 🆕 [多列排序] 新增列6(2025年薪级工资)排序: 升序
2025-07-21 11:59:06.535 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_state_changed:5796 | 🆕 [新架构多列排序] 排序状态变化: 1 列
2025-07-21 11:59:06.537 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_state_changed:5804 | 🆕 [新架构多列排序] 当前排序: 2025年薪级工资: 升序
2025-07-21 11:59:06.538 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_request:5819 | 🆕 [新架构多列排序] 排序请求: salary_data_2025_07_active_employees.grade_salary_2025 -> ascending
2025-07-21 11:59:06.541 | INFO     | src.gui.prototype.prototype_main_window:_handle_sort_applied:3522 | 🆕 [新架构排序] 处理排序应用: 列6, grade_salary_2025, ascending
2025-07-21 11:59:06.542 | INFO     | src.gui.prototype.prototype_main_window:_handle_sort_applied:3567 | 当前页码: 1
2025-07-21 11:59:06.545 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:3315 | 🔧 [排序] 使用实际页码: 1 (传入页码: 1)
2025-07-21 11:59:06.547 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:3336 | 🔧 [排序] 字段转换: grade_salary_2025 -> grade_salary_2025
2025-07-21 11:59:06.549 | INFO     | src.services.table_data_service:_handle_sort_request:140 | 处理排序请求: salary_data_2025_07_active_employees
2025-07-21 11:59:06.549 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:3355 | 🔧 [排序] 已发布排序请求事件: salary_data_2025_07_active_employees, 1列
2025-07-21 11:59:06.552 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_header_clicked:6489 | 🆕 [新架构多列排序] 成功处理列6(2025年薪级工资)点击
2025-07-21 11:59:06.552 | INFO     | src.core.unified_data_request_manager:request_table_data:200 | 开始处理数据请求: salary_data_2025_07_active_employees, 类型: sort_change
2025-07-21 11:59:06.557 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 11:59:06.559 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 11:59:06.560 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_07_active_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=1列
2025-07-21 11:59:06.562 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:516 | 🔧 [排序修复] 准备排序列: grade_salary_2025
2025-07-21 11:59:06.565 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 11:59:06.566 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:523 | 🔧 [排序修复] 列 'grade_salary_2025' 验证通过，开始排序
2025-07-21 11:59:06.568 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:532 | 🔧 [排序修复] 数值列排序: grade_salary_2025 ASC
2025-07-21 11:59:06.569 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:539 | 🔧 [排序修复] 添加排序子句: CAST("grade_salary_2025" AS REAL) ASC
2025-07-21 11:59:06.571 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | 🔧 [P0-CRITICAL修复] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_07_active_employees" ORDER BY CAST("grade_salary_2025" AS REAL) ASC LIMIT 50 OFFSET 0
2025-07-21 11:59:06.577 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:557 | 🔧 [P0-CRITICAL修复] 数据库查询返回的前5个工号: ['19961347.0', '20251003.0', '20251006.0', '20251007.0', '20251008.0']
2025-07-21 11:59:06.580 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:566 | 🔧 [P0-CRITICAL修复] 查询结果中 grade_salary_2025 的前10个值: [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 596.0]
2025-07-21 11:59:06.582 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:569 | 成功从表 salary_data_2025_07_active_employees 获取第1页数据（含排序）: 50 行，总计1396行
2025-07-21 11:59:06.585 | INFO     | src.core.unified_data_request_manager:request_table_data:249 | 数据请求处理完成: 28字段, 50行, 耗时33.1ms
2025-07-21 11:59:06.586 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:2956 | 🆕 接收到新架构数据更新事件: salary_data_2025_07_active_employees
2025-07-21 11:59:06.588 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:2981 | 数据内容: 50行 x 28列
2025-07-21 11:59:06.589 | INFO     | src.gui.prototype.prototype_main_window:set_data:685 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-21 11:59:06.591 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:1164 | 过滤了 4 个系统字段
2025-07-21 11:59:06.596 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5147 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 11:59:06.601 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 11:59:06.603 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 11:59:06.609 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:06.610 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 11:59:06.612 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 11:59:06.613 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 11:59:06.617 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 11:59:06.619 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 11:59:06.620 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 11:59:06.622 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 11:59:06.640 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 11:59:06.641 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 11:59:06.642 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 11:59:06.643 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 11:59:06.645 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 11:59:06.647 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 11:59:06.648 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 11:59:06.669 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 70.2ms
2025-07-21 11:59:06.670 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 11:59:06.671 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 11:59:06.672 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 升序
2025-07-21 11:59:06.686 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 11:59:06.687 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 50
2025-07-21 11:59:06.688 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 11:59:06.689 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3007 | 🔧 [分页修复] 数据更新事件设置总记录数: 1396
2025-07-21 11:59:06.690 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3018 | 🔧 [分页修复] 分页状态: 当前页=1, 总页数=28, 总记录数=1396
2025-07-21 11:59:06.691 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3024 | 🔧 [分页修复] 按钮可用性: 下一页=True, 上一页=False
2025-07-21 11:59:07.196 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1564 | 列宽设置已保存: salary_data_2025_07_active_employees (24 列)
2025-07-21 11:59:10.437 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3798 | 🔧 [紧急修复] 新架构分页: 第2页
2025-07-21 11:59:10.439 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3810 | 新架构分页时应用排序: 1列
2025-07-21 11:59:10.441 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 2
2025-07-21 11:59:10.442 | INFO     | src.core.unified_data_request_manager:request_table_data:200 | 开始处理数据请求: salary_data_2025_07_active_employees, 类型: initial_load
2025-07-21 11:59:10.444 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 11:59:10.445 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 11:59:10.448 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_07_active_employees 分页获取数据（支持排序）: 第2页, 每页50条, 排序=1列
2025-07-21 11:59:10.449 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:516 | 🔧 [排序修复] 准备排序列: grade_salary_2025
2025-07-21 11:59:10.450 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 11:59:10.452 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:523 | 🔧 [排序修复] 列 'grade_salary_2025' 验证通过，开始排序
2025-07-21 11:59:10.453 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:532 | 🔧 [排序修复] 数值列排序: grade_salary_2025 ASC
2025-07-21 11:59:10.454 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:539 | 🔧 [排序修复] 添加排序子句: CAST("grade_salary_2025" AS REAL) ASC
2025-07-21 11:59:10.455 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | 🔧 [P0-CRITICAL修复] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_07_active_employees" ORDER BY CAST("grade_salary_2025" AS REAL) ASC LIMIT 50 OFFSET 50
2025-07-21 11:59:10.461 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:557 | 🔧 [P0-CRITICAL修复] 数据库查询返回的前5个工号: ['20211012.0', '20221065.0', '20231054.0', '20241001.0', '20231033.0']
2025-07-21 11:59:10.462 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:566 | 🔧 [P0-CRITICAL修复] 查询结果中 grade_salary_2025 的前10个值: [1005.0, 1005.0, 1005.0, 1005.0, 1005.0, 1005.0, 1087.0, 1087.0, 1087.0, 1087.0]
2025-07-21 11:59:10.464 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:569 | 成功从表 salary_data_2025_07_active_employees 获取第2页数据（含排序）: 50 行，总计1396行
2025-07-21 11:59:10.466 | INFO     | src.core.unified_data_request_manager:request_table_data:249 | 数据请求处理完成: 28字段, 50行, 耗时23.0ms
2025-07-21 11:59:10.467 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:238 | 开始格式化工资表数据 - 表类型: active_employees
2025-07-21 11:59:10.469 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:251 | 原始数据: 50行, 28列
2025-07-21 11:59:10.470 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:255 | 🔧 [表名映射] 输入表名: active_employees, 提取的表类型: active_employees
2025-07-21 11:59:10.471 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:259 | 使用表类型配置: active_employees, 配置字段: ['id_fields', 'string_fields', 'integer_fields', 'float_fields', 'special_fields']
2025-07-21 11:59:10.473 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:263 | 🔧 [格式化调试] 开始处理ID字段: ['employee_id', 'employee_type_code', '工号', '人员类别代码']
2025-07-21 11:59:10.474 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 工号 字段样例: ['20211012.0', '20221065.0', '20231054.0']
2025-07-21 11:59:10.474 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 人员类别代码 字段样例: ['17.0', '1.0', '1.0']
2025-07-21 11:59:10.475 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 工号: 原始样例=['20211012.0', '20221065.0', '20231054.0'], 原始类型=object
2025-07-21 11:59:10.477 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 工号: 样例=['20211012', '20221065', '20231054']
2025-07-21 11:59:10.480 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 工号: 样例=['20211012', '20221065', '20231054']
2025-07-21 11:59:10.481 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 工号 格式化完成，无小数点问题
2025-07-21 11:59:10.482 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 人员类别代码: 原始样例=['17.0', '1.0', '1.0'], 原始类型=object
2025-07-21 11:59:10.483 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 人员类别代码: 样例=['17', '1', '1']
2025-07-21 11:59:10.485 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 人员类别代码: 样例=['17', '01', '01']
2025-07-21 11:59:10.486 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 人员类别代码 格式化完成，无小数点问题
2025-07-21 11:59:10.488 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 工号 字段样例: ['20211012', '20221065', '20231054']
2025-07-21 11:59:10.489 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 人员类别代码 字段样例: ['17', '01', '01']
2025-07-21 11:59:10.490 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:472 | 🔧 [强化调试] 开始格式化字符串字段: ['employee_id', 'employee_name', 'department', 'employee_type_code', 'employee_type', '工号', '姓名', '部门名称', '人员类别代码', '人员类别']
2025-07-21 11:59:10.492 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 工号: 原始样例=['20211012', '20221065', '20231054'], 原始类型=object
2025-07-21 11:59:10.493 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:504 | 🔧 [修复] 工号字段 工号 已特殊处理，去除小数点格式
2025-07-21 11:59:10.494 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 工号: 样例=['20211012', '20221065', '20231054'], 类型=object
2025-07-21 11:59:10.495 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 工号 格式化完成，无小数点问题
2025-07-21 11:59:10.497 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 姓名: 原始样例=['孙珏', '夏成启', '渠梦男'], 原始类型=object
2025-07-21 11:59:10.501 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 姓名: 样例=['孙珏', '夏成启', '渠梦男'], 类型=object
2025-07-21 11:59:10.502 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 姓名 格式化完成，无小数点问题
2025-07-21 11:59:10.504 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 部门名称: 原始样例=['经济与管理学院', '教务处', '基础医学院'], 原始类型=object
2025-07-21 11:59:10.505 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 部门名称: 样例=['经济与管理学院', '教务处', '基础医学院'], 类型=object
2025-07-21 11:59:10.506 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 部门名称 格式化完成，无小数点问题
2025-07-21 11:59:10.507 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 人员类别代码: 原始样例=['17', '01', '01'], 原始类型=object
2025-07-21 11:59:10.509 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 人员类别代码: 样例=['17', '01', '01'], 类型=object
2025-07-21 11:59:10.511 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 人员类别代码 格式化完成，无小数点问题
2025-07-21 11:59:10.512 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 人员类别: 原始样例=['管理单位人员', '管理单位人员', '教学单位专技人员'], 原始类型=object
2025-07-21 11:59:10.514 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 人员类别: 样例=['管理单位人员', '管理单位人员', '教学单位专技人员'], 类型=object
2025-07-21 11:59:10.515 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 人员类别 格式化完成，无小数点问题
2025-07-21 11:59:10.516 | INFO     | src.modules.data_storage.salary_data_formatter:_format_integer_fields:604 | 🔧 [强化调试] 开始格式化整型字段: []
2025-07-21 11:59:10.518 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:655 | 🔧 [强化调试] 开始格式化浮点型字段: ['position_salary_2025', 'grade_salary_2025', 'allowance', 'balance_allowance', 'basic_performance_2025', 'health_fee', 'transport_allowance', 'property_allowance', 'communication_allowance', 'performance_bonus_2025', 'provident_fund_2025', 'housing_allowance', 'car_allowance', 'supplement', 'advance', 'total_salary', 'pension_insurance', '2025年岗位工资', '2025年薪级工资', '津贴', '结余津贴', '2025年基础性绩效', '卫生费', '交通补贴', '物业补贴', '通讯补贴', '2025年奖励性绩效预发', '2025公积金', '住房补贴', '车补', '补发', '借支', '应发工资', '代扣代存养老保险']
2025-07-21 11:59:10.521 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年岗位工资: 原始样例=[2200.0, 1925.0, 2185.0], 原始类型=float64
2025-07-21 11:59:10.522 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年岗位工资: 样例=[2200.0, 1925.0, 2185.0], 类型=float64
2025-07-21 11:59:10.523 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年岗位工资 格式化完成，保留两位小数
2025-07-21 11:59:10.524 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年薪级工资: 原始样例=[1005.0, 1005.0, 1005.0], 原始类型=float64
2025-07-21 11:59:10.526 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年薪级工资: 样例=[1005.0, 1005.0, 1005.0], 类型=float64
2025-07-21 11:59:10.527 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年薪级工资 格式化完成，保留两位小数
2025-07-21 11:59:10.528 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 津贴: 原始样例=[0.0, 0.0, 0.0], 原始类型=float64
2025-07-21 11:59:10.531 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 津贴: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-21 11:59:10.532 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 津贴 格式化完成，保留两位小数
2025-07-21 11:59:10.533 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 结余津贴: 原始样例=[56.0, 56.0, 56.0], 原始类型=float64
2025-07-21 11:59:10.535 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 结余津贴: 样例=[56.0, 56.0, 56.0], 类型=float64
2025-07-21 11:59:10.536 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 结余津贴 格式化完成，保留两位小数
2025-07-21 11:59:10.536 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年基础性绩效: 原始样例=[2824.0, 2696.0, 4311.0], 原始类型=float64
2025-07-21 11:59:10.538 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年基础性绩效: 样例=[2824.0, 2696.0, 4311.0], 类型=float64
2025-07-21 11:59:10.540 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年基础性绩效 格式化完成，保留两位小数
2025-07-21 11:59:10.542 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 卫生费: 原始样例=[20.0, 0.0, 20.0], 原始类型=float64
2025-07-21 11:59:10.543 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 卫生费: 样例=[20.0, 0.0, 20.0], 类型=float64
2025-07-21 11:59:10.544 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 卫生费 格式化完成，保留两位小数
2025-07-21 11:59:10.545 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 交通补贴: 原始样例=[220.0, 220.0, 220.0], 原始类型=float64
2025-07-21 11:59:10.547 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 交通补贴: 样例=[220.0, 220.0, 220.0], 类型=float64
2025-07-21 11:59:10.548 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 交通补贴 格式化完成，保留两位小数
2025-07-21 11:59:10.550 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 物业补贴: 原始样例=[200.0, 200.0, 200.0], 原始类型=float64
2025-07-21 11:59:10.552 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 物业补贴: 样例=[200.0, 200.0, 200.0], 类型=float64
2025-07-21 11:59:10.553 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 物业补贴 格式化完成，保留两位小数
2025-07-21 11:59:10.553 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 通讯补贴: 原始样例=[None, None, None], 原始类型=object
2025-07-21 11:59:10.555 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 通讯补贴: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-21 11:59:10.556 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 通讯补贴 格式化完成，保留两位小数
2025-07-21 11:59:10.557 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年奖励性绩效预发: 原始样例=[500.0, 1500.0, 1600.0], 原始类型=float64
2025-07-21 11:59:10.560 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年奖励性绩效预发: 样例=[500.0, 1500.0, 1600.0], 类型=float64
2025-07-21 11:59:10.561 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年奖励性绩效预发 格式化完成，保留两位小数
2025-07-21 11:59:10.562 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025公积金: 原始样例=[1306.0, 1080.0, 1268.0], 原始类型=float64
2025-07-21 11:59:10.564 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025公积金: 样例=[1306.0, 1080.0, 1268.0], 类型=float64
2025-07-21 11:59:10.565 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025公积金 格式化完成，保留两位小数
2025-07-21 11:59:10.566 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 住房补贴: 原始样例=[143.0, 143.0, 189.0], 原始类型=float64
2025-07-21 11:59:10.567 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 住房补贴: 样例=[143.0, 143.0, 189.0], 类型=float64
2025-07-21 11:59:10.570 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 住房补贴 格式化完成，保留两位小数
2025-07-21 11:59:10.571 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 车补: 原始样例=[None, None, None], 原始类型=object
2025-07-21 11:59:10.573 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 车补: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-21 11:59:10.574 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 车补 格式化完成，保留两位小数
2025-07-21 11:59:10.575 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 补发: 原始样例=[None, None, None], 原始类型=object
2025-07-21 11:59:10.577 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 补发: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-21 11:59:10.578 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 补发 格式化完成，保留两位小数
2025-07-21 11:59:10.581 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 借支: 原始样例=[nan, nan, nan], 原始类型=float64
2025-07-21 11:59:10.583 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 借支: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-21 11:59:10.583 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 借支 格式化完成，保留两位小数
2025-07-21 11:59:10.585 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 应发工资: 原始样例=[7168.0, 7745.0, 9786.0], 原始类型=float64
2025-07-21 11:59:10.586 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 应发工资: 样例=[7168.0, 7745.0, 9786.0], 类型=float64
2025-07-21 11:59:10.587 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 应发工资 格式化完成，保留两位小数
2025-07-21 11:59:10.588 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 代扣代存养老保险: 原始样例=[978.47, 961.93, 971.8], 原始类型=float64
2025-07-21 11:59:10.591 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 代扣代存养老保险: 样例=[978.47, 961.93, 971.8], 类型=float64
2025-07-21 11:59:10.592 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 代扣代存养老保险 格式化完成，保留两位小数
2025-07-21 11:59:10.593 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:711 | 🔧 [特殊字段] 开始处理表类型 active_employees 的月份、年份字段
2025-07-21 11:59:10.594 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:866 | 🔧 [特殊字段] 月份、年份字段处理完成
2025-07-21 11:59:10.597 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:291 | 数据格式化完成: 50行, 28列
2025-07-21 11:59:10.598 | INFO     | src.modules.format_management.master_format_manager:format_table_data:187 | 🎯 [统一格式化] 数据格式化完成: active_employees
2025-07-21 11:59:10.600 | INFO     | src.services.table_data_service:load_table_data:347 | 🎯 [统一格式化] 数据格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:10.602 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:2956 | 🆕 接收到新架构数据更新事件: salary_data_2025_07_active_employees
2025-07-21 11:59:10.603 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:2981 | 数据内容: 50行 x 28列
2025-07-21 11:59:10.604 | INFO     | src.gui.prototype.prototype_main_window:set_data:685 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-21 11:59:10.606 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:1164 | 过滤了 4 个系统字段
2025-07-21 11:59:10.608 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5147 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 11:59:10.612 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 11:59:10.613 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 11:59:10.617 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:10.619 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 11:59:10.621 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 11:59:10.622 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 11:59:10.623 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 11:59:10.624 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 11:59:10.625 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 11:59:10.626 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 11:59:10.628 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 11:59:10.630 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 11:59:10.632 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 11:59:10.633 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 11:59:10.634 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 11:59:10.635 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 11:59:10.636 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 11:59:10.646 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 35.1ms
2025-07-21 11:59:10.647 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 11:59:10.648 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 11:59:10.650 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 升序
2025-07-21 11:59:10.658 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 11:59:10.659 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 50
2025-07-21 11:59:10.660 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3798 | 🔧 [紧急修复] 新架构分页: 第1页
2025-07-21 11:59:10.661 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3810 | 新架构分页时应用排序: 1列
2025-07-21 11:59:10.663 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 1
2025-07-21 11:59:10.664 | INFO     | src.core.unified_data_request_manager:request_table_data:200 | 开始处理数据请求: salary_data_2025_07_active_employees, 类型: initial_load
2025-07-21 11:59:10.665 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 11:59:10.666 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 11:59:10.669 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_07_active_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=1列
2025-07-21 11:59:10.670 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:516 | 🔧 [排序修复] 准备排序列: grade_salary_2025
2025-07-21 11:59:10.671 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 11:59:10.672 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:523 | 🔧 [排序修复] 列 'grade_salary_2025' 验证通过，开始排序
2025-07-21 11:59:10.673 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:532 | 🔧 [排序修复] 数值列排序: grade_salary_2025 ASC
2025-07-21 11:59:10.674 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:539 | 🔧 [排序修复] 添加排序子句: CAST("grade_salary_2025" AS REAL) ASC
2025-07-21 11:59:10.675 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | 🔧 [P0-CRITICAL修复] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_07_active_employees" ORDER BY CAST("grade_salary_2025" AS REAL) ASC LIMIT 50 OFFSET 0
2025-07-21 11:59:10.680 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:557 | 🔧 [P0-CRITICAL修复] 数据库查询返回的前5个工号: ['19961347.0', '20251003.0', '20251006.0', '20251007.0', '20251008.0']
2025-07-21 11:59:10.682 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:566 | 🔧 [P0-CRITICAL修复] 查询结果中 grade_salary_2025 的前10个值: [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 596.0]
2025-07-21 11:59:10.683 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:569 | 成功从表 salary_data_2025_07_active_employees 获取第1页数据（含排序）: 50 行，总计1396行
2025-07-21 11:59:10.685 | INFO     | src.core.unified_data_request_manager:request_table_data:249 | 数据请求处理完成: 28字段, 50行, 耗时21.0ms
2025-07-21 11:59:10.687 | INFO     | src.services.table_data_service:load_table_data:347 | 🎯 [统一格式化] 数据格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:10.692 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5147 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 11:59:10.695 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 11:59:10.696 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 11:59:10.702 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:10.703 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 11:59:10.704 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 11:59:10.705 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 11:59:10.706 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 11:59:10.709 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 11:59:10.710 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 11:59:10.711 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 11:59:10.713 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 11:59:10.714 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 11:59:10.715 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 11:59:10.716 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 11:59:10.719 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 11:59:10.720 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 11:59:10.721 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 11:59:10.730 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 36.1ms
2025-07-21 11:59:10.731 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 11:59:10.732 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 11:59:10.733 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 升序
2025-07-21 11:59:10.735 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 11:59:10.736 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 11:59:10.738 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3873 | 🔧 [紧急修复] 新架构分页成功: 第1页, 50行
2025-07-21 11:59:10.739 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3798 | 🔧 [紧急修复] 新架构分页: 第1页
2025-07-21 11:59:10.741 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3810 | 新架构分页时应用排序: 1列
2025-07-21 11:59:10.742 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 1
2025-07-21 11:59:10.748 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5147 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 11:59:10.751 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 11:59:10.752 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 11:59:10.756 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:10.758 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 11:59:10.759 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 11:59:10.760 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 11:59:10.761 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 11:59:10.762 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 11:59:10.763 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 11:59:10.765 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 11:59:10.767 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 11:59:10.769 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 11:59:10.770 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 11:59:10.771 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 11:59:10.772 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 11:59:10.774 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 11:59:10.775 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 11:59:10.784 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 33.1ms
2025-07-21 11:59:10.785 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 11:59:10.786 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 11:59:10.788 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 升序
2025-07-21 11:59:10.789 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 11:59:10.790 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 11:59:10.791 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3873 | 🔧 [紧急修复] 新架构分页成功: 第1页, 50行
2025-07-21 11:59:10.792 | INFO     | src.gui.widgets.pagination_widget:set_current_page:330 | 页码切换到: 1
2025-07-21 11:59:10.794 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3798 | 🔧 [紧急修复] 新架构分页: 第2页
2025-07-21 11:59:10.795 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3810 | 新架构分页时应用排序: 1列
2025-07-21 11:59:10.798 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 2
2025-07-21 11:59:10.802 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5147 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 11:59:10.805 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 11:59:10.807 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 11:59:10.812 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:10.813 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 11:59:10.814 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 11:59:10.815 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 11:59:10.817 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 11:59:10.818 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 11:59:10.820 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 11:59:10.821 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 11:59:10.823 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 11:59:10.824 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 11:59:10.825 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 11:59:10.827 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 11:59:10.829 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 11:59:10.830 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 11:59:10.831 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 11:59:10.840 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 36.1ms
2025-07-21 11:59:10.841 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 11:59:10.843 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 11:59:10.844 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 升序
2025-07-21 11:59:10.845 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 11:59:10.847 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 11:59:10.848 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3873 | 🔧 [紧急修复] 新架构分页成功: 第2页, 50行
2025-07-21 11:59:10.850 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3798 | 🔧 [紧急修复] 新架构分页: 第2页
2025-07-21 11:59:10.851 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3810 | 新架构分页时应用排序: 1列
2025-07-21 11:59:10.852 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 2
2025-07-21 11:59:10.858 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5147 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 11:59:10.861 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 11:59:10.862 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 11:59:10.866 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:10.868 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 11:59:10.869 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 11:59:10.870 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 11:59:10.871 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 11:59:10.872 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 11:59:10.873 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 11:59:10.874 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 11:59:10.877 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 11:59:10.878 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 11:59:10.879 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 11:59:10.880 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 11:59:10.882 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 11:59:10.883 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 11:59:10.884 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 11:59:10.893 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 33.1ms
2025-07-21 11:59:10.894 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 11:59:10.895 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 11:59:10.897 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 升序
2025-07-21 11:59:10.898 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 11:59:10.899 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 11:59:10.900 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3873 | 🔧 [紧急修复] 新架构分页成功: 第2页, 50行
2025-07-21 11:59:10.902 | INFO     | src.gui.widgets.pagination_widget:set_current_page:330 | 页码切换到: 2
2025-07-21 11:59:10.902 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3012 | 🔧 [分页修复] 数据更新事件设置当前页: 2
2025-07-21 11:59:10.904 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3018 | 🔧 [分页修复] 分页状态: 当前页=2, 总页数=28, 总记录数=1396
2025-07-21 11:59:10.907 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3024 | 🔧 [分页修复] 按钮可用性: 下一页=True, 上一页=True
2025-07-21 11:59:10.911 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5147 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 11:59:10.915 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 11:59:10.917 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 11:59:10.921 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:10.922 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 11:59:10.923 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 11:59:10.924 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 11:59:10.927 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 11:59:10.928 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 11:59:10.929 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 11:59:10.930 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 11:59:10.932 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 11:59:10.933 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 11:59:10.934 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 11:59:10.937 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 11:59:10.938 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 11:59:10.940 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 11:59:10.941 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 11:59:10.950 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 37.2ms
2025-07-21 11:59:10.951 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 11:59:10.952 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 11:59:10.953 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 升序
2025-07-21 11:59:10.954 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 11:59:10.956 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 11:59:10.957 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3873 | 🔧 [紧急修复] 新架构分页成功: 第2页, 50行
2025-07-21 11:59:10.959 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3798 | 🔧 [紧急修复] 新架构分页: 第2页
2025-07-21 11:59:10.960 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3810 | 新架构分页时应用排序: 1列
2025-07-21 11:59:10.961 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 2
2025-07-21 11:59:10.967 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5147 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 11:59:10.970 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 11:59:10.971 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 11:59:10.975 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:10.977 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 11:59:10.978 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 11:59:10.979 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 11:59:10.980 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 11:59:10.981 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 11:59:10.982 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 11:59:10.983 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 11:59:10.985 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 11:59:10.987 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 11:59:10.988 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 11:59:10.989 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 11:59:10.991 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 11:59:10.992 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 11:59:10.993 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 11:59:11.002 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 33.1ms
2025-07-21 11:59:11.003 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 11:59:11.004 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 11:59:11.006 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 升序
2025-07-21 11:59:11.008 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 11:59:11.008 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 11:59:11.010 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3873 | 🔧 [紧急修复] 新架构分页成功: 第2页, 50行
2025-07-21 11:59:11.011 | INFO     | src.gui.widgets.pagination_widget:set_current_page:330 | 页码切换到: 2
2025-07-21 11:59:11.532 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1564 | 列宽设置已保存: salary_data_2025_07_active_employees (24 列)
2025-07-21 11:59:15.148 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3798 | 🔧 [紧急修复] 新架构分页: 第3页
2025-07-21 11:59:15.151 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3810 | 新架构分页时应用排序: 1列
2025-07-21 11:59:15.153 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 3
2025-07-21 11:59:15.154 | INFO     | src.core.unified_data_request_manager:request_table_data:200 | 开始处理数据请求: salary_data_2025_07_active_employees, 类型: initial_load
2025-07-21 11:59:15.155 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 11:59:15.156 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 11:59:15.157 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_07_active_employees 分页获取数据（支持排序）: 第3页, 每页50条, 排序=1列
2025-07-21 11:59:15.160 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:516 | 🔧 [排序修复] 准备排序列: grade_salary_2025
2025-07-21 11:59:15.161 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 11:59:15.162 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:523 | 🔧 [排序修复] 列 'grade_salary_2025' 验证通过，开始排序
2025-07-21 11:59:15.163 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:532 | 🔧 [排序修复] 数值列排序: grade_salary_2025 ASC
2025-07-21 11:59:15.164 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:539 | 🔧 [排序修复] 添加排序子句: CAST("grade_salary_2025" AS REAL) ASC
2025-07-21 11:59:15.165 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | 🔧 [P0-CRITICAL修复] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_07_active_employees" ORDER BY CAST("grade_salary_2025" AS REAL) ASC LIMIT 50 OFFSET 100
2025-07-21 11:59:15.169 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:557 | 🔧 [P0-CRITICAL修复] 数据库查询返回的前5个工号: ['20191750.0', '20211013.0', '20221042.0', '20221043.0', '20221002.0']
2025-07-21 11:59:15.171 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:566 | 🔧 [P0-CRITICAL修复] 查询结果中 grade_salary_2025 的前10个值: [1169.0, 1169.0, 1169.0, 1169.0, 1169.0, 1169.0, 1169.0, 1169.0, 1169.0, 1169.0]
2025-07-21 11:59:15.173 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:569 | 成功从表 salary_data_2025_07_active_employees 获取第3页数据（含排序）: 50 行，总计1396行
2025-07-21 11:59:15.174 | INFO     | src.core.unified_data_request_manager:request_table_data:249 | 数据请求处理完成: 28字段, 50行, 耗时20.0ms
2025-07-21 11:59:15.176 | INFO     | src.services.table_data_service:load_table_data:347 | 🎯 [统一格式化] 数据格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:15.178 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:2956 | 🆕 接收到新架构数据更新事件: salary_data_2025_07_active_employees
2025-07-21 11:59:15.180 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:2981 | 数据内容: 50行 x 28列
2025-07-21 11:59:15.181 | INFO     | src.gui.prototype.prototype_main_window:set_data:685 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-21 11:59:15.183 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:1164 | 过滤了 4 个系统字段
2025-07-21 11:59:15.185 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5147 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 11:59:15.188 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 11:59:15.190 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 11:59:15.194 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:15.195 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 11:59:15.197 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 11:59:15.199 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 11:59:15.200 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 11:59:15.201 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 11:59:15.203 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 11:59:15.203 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 11:59:15.205 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 11:59:15.206 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 11:59:15.207 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 11:59:15.210 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 11:59:15.211 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 11:59:15.213 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 11:59:15.215 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 11:59:15.254 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 66.7ms
2025-07-21 11:59:15.256 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 11:59:15.257 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 11:59:15.258 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 升序
2025-07-21 11:59:15.267 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 11:59:15.268 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 50
2025-07-21 11:59:15.269 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3798 | 🔧 [紧急修复] 新架构分页: 第1页
2025-07-21 11:59:15.270 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3810 | 新架构分页时应用排序: 1列
2025-07-21 11:59:15.271 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 1
2025-07-21 11:59:15.277 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5147 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 11:59:15.280 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 11:59:15.281 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 11:59:15.285 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:15.286 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 11:59:15.288 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 11:59:15.289 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 11:59:15.290 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 11:59:15.291 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 11:59:15.292 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 11:59:15.293 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 11:59:15.297 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 11:59:15.298 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 11:59:15.299 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 11:59:15.300 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 11:59:15.301 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 11:59:15.302 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 11:59:15.303 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 11:59:15.314 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 35.1ms
2025-07-21 11:59:15.315 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 11:59:15.316 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 11:59:15.317 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 升序
2025-07-21 11:59:15.319 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 11:59:15.320 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 11:59:15.321 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3873 | 🔧 [紧急修复] 新架构分页成功: 第1页, 50行
2025-07-21 11:59:15.322 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3798 | 🔧 [紧急修复] 新架构分页: 第1页
2025-07-21 11:59:15.325 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3810 | 新架构分页时应用排序: 1列
2025-07-21 11:59:15.326 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 1
2025-07-21 11:59:15.331 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5147 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 11:59:15.333 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 11:59:15.336 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 11:59:15.340 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:15.341 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 11:59:15.342 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 11:59:15.343 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 11:59:15.345 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 11:59:15.347 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 11:59:15.348 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 11:59:15.349 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 11:59:15.351 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 11:59:15.352 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 11:59:15.353 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 11:59:15.355 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 11:59:15.357 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 11:59:15.358 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 11:59:15.359 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 11:59:15.368 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 36.1ms
2025-07-21 11:59:15.369 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 11:59:15.370 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 11:59:15.371 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 升序
2025-07-21 11:59:15.373 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 11:59:15.375 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 11:59:15.376 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3873 | 🔧 [紧急修复] 新架构分页成功: 第1页, 50行
2025-07-21 11:59:15.377 | INFO     | src.gui.widgets.pagination_widget:set_current_page:330 | 页码切换到: 1
2025-07-21 11:59:15.378 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3798 | 🔧 [紧急修复] 新架构分页: 第3页
2025-07-21 11:59:15.379 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3810 | 新架构分页时应用排序: 1列
2025-07-21 11:59:15.380 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 3
2025-07-21 11:59:15.386 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5147 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 11:59:15.389 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 11:59:15.390 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 11:59:15.394 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:15.396 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 11:59:15.397 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 11:59:15.398 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 11:59:15.399 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 11:59:15.400 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 11:59:15.401 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 11:59:15.402 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 11:59:15.404 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 11:59:15.406 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 11:59:15.407 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 11:59:15.408 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 11:59:15.409 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 11:59:15.411 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 11:59:15.412 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 11:59:15.421 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 33.1ms
2025-07-21 11:59:15.422 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 11:59:15.423 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 11:59:15.425 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 升序
2025-07-21 11:59:15.426 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 11:59:15.427 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 11:59:15.428 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3873 | 🔧 [紧急修复] 新架构分页成功: 第3页, 50行
2025-07-21 11:59:15.430 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3798 | 🔧 [紧急修复] 新架构分页: 第3页
2025-07-21 11:59:15.431 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3810 | 新架构分页时应用排序: 1列
2025-07-21 11:59:15.432 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 3
2025-07-21 11:59:15.438 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5147 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 11:59:15.441 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 11:59:15.442 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 11:59:15.448 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:15.449 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 11:59:15.450 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 11:59:15.451 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 11:59:15.452 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 11:59:15.454 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 11:59:15.455 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 11:59:15.457 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 11:59:15.459 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 11:59:15.460 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 11:59:15.461 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 11:59:15.462 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 11:59:15.465 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 11:59:15.466 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 11:59:15.467 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 11:59:15.476 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 36.1ms
2025-07-21 11:59:15.477 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 11:59:15.478 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 11:59:15.479 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 升序
2025-07-21 11:59:15.481 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 11:59:15.483 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 11:59:15.484 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3873 | 🔧 [紧急修复] 新架构分页成功: 第3页, 50行
2025-07-21 11:59:15.485 | INFO     | src.gui.widgets.pagination_widget:set_current_page:330 | 页码切换到: 3
2025-07-21 11:59:15.486 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3012 | 🔧 [分页修复] 数据更新事件设置当前页: 3
2025-07-21 11:59:15.487 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3018 | 🔧 [分页修复] 分页状态: 当前页=3, 总页数=28, 总记录数=1396
2025-07-21 11:59:15.488 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3024 | 🔧 [分页修复] 按钮可用性: 下一页=True, 上一页=True
2025-07-21 11:59:15.494 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5147 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 11:59:15.497 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 11:59:15.498 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 11:59:15.502 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:15.504 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 11:59:15.505 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 11:59:15.506 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 11:59:15.507 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 11:59:15.508 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 11:59:15.509 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 11:59:15.510 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 11:59:15.512 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 11:59:15.514 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 11:59:15.516 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 11:59:15.517 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 11:59:15.518 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 11:59:15.520 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 11:59:15.521 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 11:59:15.530 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 33.1ms
2025-07-21 11:59:15.531 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 11:59:15.532 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 11:59:15.534 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 升序
2025-07-21 11:59:15.536 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 11:59:15.537 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 11:59:15.538 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3873 | 🔧 [紧急修复] 新架构分页成功: 第3页, 50行
2025-07-21 11:59:15.539 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3798 | 🔧 [紧急修复] 新架构分页: 第3页
2025-07-21 11:59:15.540 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3810 | 新架构分页时应用排序: 1列
2025-07-21 11:59:15.541 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 3
2025-07-21 11:59:15.548 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5147 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 11:59:15.551 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 11:59:15.552 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 11:59:15.557 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:15.559 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 11:59:15.560 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 11:59:15.561 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 11:59:15.562 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 11:59:15.564 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 11:59:15.566 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 11:59:15.567 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 11:59:15.569 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 11:59:15.570 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 11:59:15.571 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 11:59:15.572 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 11:59:15.575 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 11:59:15.576 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 11:59:15.577 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 11:59:15.586 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 36.1ms
2025-07-21 11:59:15.587 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 11:59:15.588 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 11:59:15.589 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 升序
2025-07-21 11:59:15.591 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 11:59:15.592 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 11:59:15.593 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3873 | 🔧 [紧急修复] 新架构分页成功: 第3页, 50行
2025-07-21 11:59:15.595 | INFO     | src.gui.widgets.pagination_widget:set_current_page:330 | 页码切换到: 3
2025-07-21 11:59:16.086 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1564 | 列宽设置已保存: salary_data_2025_07_active_employees (24 列)
2025-07-21 11:59:20.749 | INFO     | src.gui.prototype.widgets.column_sort_manager:handle_header_click:128 | 🆕 [多列排序] 更新列6(2025年薪级工资)排序: descending
2025-07-21 11:59:20.751 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_state_changed:5796 | 🆕 [新架构多列排序] 排序状态变化: 1 列
2025-07-21 11:59:20.753 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_state_changed:5804 | 🆕 [新架构多列排序] 当前排序: 2025年薪级工资: 降序
2025-07-21 11:59:20.754 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_request:5819 | 🆕 [新架构多列排序] 排序请求: salary_data_2025_07_active_employees.grade_salary_2025 -> descending
2025-07-21 11:59:20.756 | INFO     | src.gui.prototype.prototype_main_window:_handle_sort_applied:3522 | 🆕 [新架构排序] 处理排序应用: 列6, grade_salary_2025, descending
2025-07-21 11:59:20.759 | INFO     | src.core.table_sort_state_manager:save_sort_state:229 | 已保存排序状态: salary_data_2025_07_active_employees (employees), 1 列
2025-07-21 11:59:20.761 | INFO     | src.gui.prototype.prototype_main_window:_handle_sort_applied:3561 | 表头排序状态已同步到管理器
2025-07-21 11:59:20.763 | INFO     | src.gui.prototype.prototype_main_window:_handle_sort_applied:3567 | 当前页码: 3
2025-07-21 11:59:20.764 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:3315 | 🔧 [排序] 使用实际页码: 3 (传入页码: 3)
2025-07-21 11:59:20.765 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:3336 | 🔧 [排序] 字段转换: grade_salary_2025 -> grade_salary_2025
2025-07-21 11:59:20.767 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:3355 | 🔧 [排序] 已发布排序请求事件: salary_data_2025_07_active_employees, 1列
2025-07-21 11:59:20.767 | INFO     | src.services.table_data_service:_handle_sort_request:140 | 处理排序请求: salary_data_2025_07_active_employees
2025-07-21 11:59:20.768 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_header_clicked:6489 | 🆕 [新架构多列排序] 成功处理列6(2025年薪级工资)点击
2025-07-21 11:59:20.772 | INFO     | src.core.unified_data_request_manager:request_table_data:200 | 开始处理数据请求: salary_data_2025_07_active_employees, 类型: sort_change
2025-07-21 11:59:20.775 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 11:59:20.776 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 11:59:20.778 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_07_active_employees 分页获取数据（支持排序）: 第3页, 每页50条, 排序=1列
2025-07-21 11:59:20.781 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:516 | 🔧 [排序修复] 准备排序列: grade_salary_2025
2025-07-21 11:59:20.783 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 11:59:20.784 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:523 | 🔧 [排序修复] 列 'grade_salary_2025' 验证通过，开始排序
2025-07-21 11:59:20.786 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:532 | 🔧 [排序修复] 数值列排序: grade_salary_2025 DESC
2025-07-21 11:59:20.787 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:539 | 🔧 [排序修复] 添加排序子句: CAST("grade_salary_2025" AS REAL) DESC
2025-07-21 11:59:20.789 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | 🔧 [P0-CRITICAL修复] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_07_active_employees" ORDER BY CAST("grade_salary_2025" AS REAL) DESC LIMIT 50 OFFSET 100
2025-07-21 11:59:20.795 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:557 | 🔧 [P0-CRITICAL修复] 数据库查询返回的前5个工号: ['19840232.0', '19870226.0', '19920265.0', '19890293.0', '19921156.0']
2025-07-21 11:59:20.797 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:566 | 🔧 [P0-CRITICAL修复] 查询结果中 grade_salary_2025 的前10个值: [3641.0, 3641.0, 3641.0, 3641.0, 3641.0, 3641.0, 3641.0, 3641.0, 3641.0, 3641.0]
2025-07-21 11:59:20.799 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:569 | 成功从表 salary_data_2025_07_active_employees 获取第3页数据（含排序）: 50 行，总计1396行
2025-07-21 11:59:20.802 | INFO     | src.core.unified_data_request_manager:request_table_data:249 | 数据请求处理完成: 28字段, 50行, 耗时29.5ms
2025-07-21 11:59:20.803 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:2956 | 🆕 接收到新架构数据更新事件: salary_data_2025_07_active_employees
2025-07-21 11:59:20.804 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:2981 | 数据内容: 50行 x 28列
2025-07-21 11:59:20.805 | INFO     | src.gui.prototype.prototype_main_window:set_data:685 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-21 11:59:20.806 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:1164 | 过滤了 4 个系统字段
2025-07-21 11:59:20.808 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5147 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 11:59:20.811 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 11:59:20.812 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 11:59:20.816 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:20.818 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 11:59:20.819 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 11:59:20.822 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 11:59:20.823 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 11:59:20.826 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 11:59:20.827 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 11:59:20.828 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 11:59:20.830 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 11:59:20.832 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 11:59:20.833 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 11:59:20.834 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 11:59:20.836 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 11:59:20.837 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 11:59:20.838 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 11:59:20.847 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 37.1ms
2025-07-21 11:59:20.848 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 11:59:20.850 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 11:59:20.852 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 降序
2025-07-21 11:59:20.864 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 11:59:20.865 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 50
2025-07-21 11:59:20.866 | INFO     | src.gui.widgets.pagination_widget:set_current_page:330 | 页码切换到: 1
2025-07-21 11:59:20.867 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 11:59:20.868 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3007 | 🔧 [分页修复] 数据更新事件设置总记录数: 1396
2025-07-21 11:59:20.869 | INFO     | src.gui.widgets.pagination_widget:set_current_page:330 | 页码切换到: 3
2025-07-21 11:59:20.871 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3012 | 🔧 [分页修复] 数据更新事件设置当前页: 3
2025-07-21 11:59:20.872 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3018 | 🔧 [分页修复] 分页状态: 当前页=3, 总页数=28, 总记录数=1396
2025-07-21 11:59:20.873 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3024 | 🔧 [分页修复] 按钮可用性: 下一页=True, 上一页=True
2025-07-21 11:59:20.879 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3798 | 🔧 [紧急修复] 新架构分页: 第1页
2025-07-21 11:59:20.881 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3810 | 新架构分页时应用排序: 1列
2025-07-21 11:59:20.883 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 1
2025-07-21 11:59:20.884 | INFO     | src.core.unified_data_request_manager:request_table_data:200 | 开始处理数据请求: salary_data_2025_07_active_employees, 类型: initial_load
2025-07-21 11:59:20.885 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 11:59:20.886 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 11:59:20.887 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_07_active_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=1列
2025-07-21 11:59:20.888 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:516 | 🔧 [排序修复] 准备排序列: grade_salary_2025
2025-07-21 11:59:20.890 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 11:59:20.893 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:523 | 🔧 [排序修复] 列 'grade_salary_2025' 验证通过，开始排序
2025-07-21 11:59:20.894 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:532 | 🔧 [排序修复] 数值列排序: grade_salary_2025 DESC
2025-07-21 11:59:20.895 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:539 | 🔧 [排序修复] 添加排序子句: CAST("grade_salary_2025" AS REAL) DESC
2025-07-21 11:59:20.896 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | 🔧 [P0-CRITICAL修复] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_07_active_employees" ORDER BY CAST("grade_salary_2025" AS REAL) DESC LIMIT 50 OFFSET 0
2025-07-21 11:59:20.900 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:557 | 🔧 [P0-CRITICAL修复] 数据库查询返回的前5个工号: ['19850005.0', '19860112.0', '19870288.0', '19870673.0', '19870883.0']
2025-07-21 11:59:20.903 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:566 | 🔧 [P0-CRITICAL修复] 查询结果中 grade_salary_2025 的前10个值: [6071.0, 5441.0, 5081.0, 4931.0, 4781.0, 4631.0, 4631.0, 4631.0, 4631.0, 4481.0]
2025-07-21 11:59:20.905 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:569 | 成功从表 salary_data_2025_07_active_employees 获取第1页数据（含排序）: 50 行，总计1396行
2025-07-21 11:59:20.906 | INFO     | src.core.unified_data_request_manager:request_table_data:249 | 数据请求处理完成: 28字段, 50行, 耗时22.0ms
2025-07-21 11:59:20.908 | INFO     | src.services.table_data_service:load_table_data:347 | 🎯 [统一格式化] 数据格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:20.910 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:2956 | 🆕 接收到新架构数据更新事件: salary_data_2025_07_active_employees
2025-07-21 11:59:20.911 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:2981 | 数据内容: 50行 x 28列
2025-07-21 11:59:20.913 | INFO     | src.gui.prototype.prototype_main_window:set_data:685 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-21 11:59:20.915 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:1164 | 过滤了 4 个系统字段
2025-07-21 11:59:20.917 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5147 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 11:59:20.920 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 11:59:20.922 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 11:59:20.926 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:20.928 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 11:59:20.929 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 11:59:20.930 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 11:59:20.932 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 11:59:20.933 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 11:59:20.934 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 11:59:20.935 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 11:59:20.937 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 11:59:20.938 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 11:59:20.940 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 11:59:20.941 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 11:59:20.943 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 11:59:20.945 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 11:59:20.946 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 11:59:20.955 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 36.1ms
2025-07-21 11:59:20.956 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 11:59:20.958 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 11:59:20.959 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 降序
2025-07-21 11:59:20.960 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 11:59:20.962 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 50
2025-07-21 11:59:20.963 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3798 | 🔧 [紧急修复] 新架构分页: 第1页
2025-07-21 11:59:20.964 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3810 | 新架构分页时应用排序: 1列
2025-07-21 11:59:20.966 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 1
2025-07-21 11:59:20.972 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5147 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 11:59:20.975 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 11:59:20.976 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 11:59:20.980 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:20.982 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 11:59:20.983 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 11:59:20.984 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 11:59:20.985 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 11:59:20.986 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 11:59:20.987 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 11:59:20.988 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 11:59:20.990 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 11:59:20.992 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 11:59:20.994 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 11:59:20.995 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 11:59:20.996 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 11:59:20.997 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 11:59:20.998 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 11:59:21.007 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 33.1ms
2025-07-21 11:59:21.009 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 11:59:21.010 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 11:59:21.011 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 降序
2025-07-21 11:59:21.013 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 11:59:21.014 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 11:59:21.015 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3873 | 🔧 [紧急修复] 新架构分页成功: 第1页, 50行
2025-07-21 11:59:21.016 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3798 | 🔧 [紧急修复] 新架构分页: 第1页
2025-07-21 11:59:21.017 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3810 | 新架构分页时应用排序: 1列
2025-07-21 11:59:21.019 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 1
2025-07-21 11:59:21.025 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5147 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 11:59:21.028 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 11:59:21.029 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 11:59:21.034 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:21.035 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 11:59:21.036 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 11:59:21.037 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 11:59:21.039 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 11:59:21.041 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 11:59:21.042 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 11:59:21.043 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 11:59:21.045 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 11:59:21.046 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 11:59:21.047 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 11:59:21.048 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 11:59:21.050 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 11:59:21.053 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 11:59:21.054 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 11:59:21.063 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 36.1ms
2025-07-21 11:59:21.064 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 11:59:21.065 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 11:59:21.066 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 降序
2025-07-21 11:59:21.068 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 11:59:21.070 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 11:59:21.071 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3873 | 🔧 [紧急修复] 新架构分页成功: 第1页, 50行
2025-07-21 11:59:21.072 | INFO     | src.gui.widgets.pagination_widget:set_current_page:330 | 页码切换到: 1
2025-07-21 11:59:21.073 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3018 | 🔧 [分页修复] 分页状态: 当前页=1, 总页数=28, 总记录数=1396
2025-07-21 11:59:21.074 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3024 | 🔧 [分页修复] 按钮可用性: 下一页=True, 上一页=False
2025-07-21 11:59:21.078 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5147 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 11:59:21.084 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 11:59:21.085 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 11:59:21.089 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:21.091 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 11:59:21.092 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 11:59:21.094 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 11:59:21.095 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 11:59:21.096 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 11:59:21.097 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 11:59:21.098 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 11:59:21.100 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 11:59:21.102 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 11:59:21.103 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 11:59:21.104 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 11:59:21.106 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 11:59:21.107 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 11:59:21.108 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 11:59:21.117 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 34.1ms
2025-07-21 11:59:21.118 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 11:59:21.119 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 11:59:21.121 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 降序
2025-07-21 11:59:21.123 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 11:59:21.124 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 11:59:21.125 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3873 | 🔧 [紧急修复] 新架构分页成功: 第1页, 50行
2025-07-21 11:59:21.126 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3798 | 🔧 [紧急修复] 新架构分页: 第1页
2025-07-21 11:59:21.127 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3810 | 新架构分页时应用排序: 1列
2025-07-21 11:59:21.129 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 1
2025-07-21 11:59:21.135 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5147 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 11:59:21.137 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 11:59:21.138 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 11:59:21.144 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:21.145 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 11:59:21.146 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 11:59:21.148 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 11:59:21.149 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 11:59:21.151 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 11:59:21.152 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 11:59:21.153 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 11:59:21.155 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 11:59:21.156 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 11:59:21.157 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 11:59:21.158 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 11:59:21.159 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 11:59:21.160 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 11:59:21.162 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 11:59:21.171 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 34.1ms
2025-07-21 11:59:21.172 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 11:59:21.172 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 11:59:21.173 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 降序
2025-07-21 11:59:21.174 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 11:59:21.175 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 11:59:21.175 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3873 | 🔧 [紧急修复] 新架构分页成功: 第1页, 50行
2025-07-21 11:59:21.176 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3798 | 🔧 [紧急修复] 新架构分页: 第3页
2025-07-21 11:59:21.177 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3810 | 新架构分页时应用排序: 1列
2025-07-21 11:59:21.178 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 3
2025-07-21 11:59:21.178 | INFO     | src.core.unified_data_request_manager:request_table_data:200 | 开始处理数据请求: salary_data_2025_07_active_employees, 类型: initial_load
2025-07-21 11:59:21.181 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 11:59:21.182 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 11:59:21.183 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_07_active_employees 分页获取数据（支持排序）: 第3页, 每页50条, 排序=1列
2025-07-21 11:59:21.183 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:516 | 🔧 [排序修复] 准备排序列: grade_salary_2025
2025-07-21 11:59:21.184 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 11:59:21.185 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:523 | 🔧 [排序修复] 列 'grade_salary_2025' 验证通过，开始排序
2025-07-21 11:59:21.185 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:532 | 🔧 [排序修复] 数值列排序: grade_salary_2025 DESC
2025-07-21 11:59:21.186 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:539 | 🔧 [排序修复] 添加排序子句: CAST("grade_salary_2025" AS REAL) DESC
2025-07-21 11:59:21.187 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | 🔧 [P0-CRITICAL修复] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_07_active_employees" ORDER BY CAST("grade_salary_2025" AS REAL) DESC LIMIT 50 OFFSET 100
2025-07-21 11:59:21.189 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:557 | 🔧 [P0-CRITICAL修复] 数据库查询返回的前5个工号: ['19840232.0', '19870226.0', '19920265.0', '19890293.0', '19921156.0']
2025-07-21 11:59:21.192 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:566 | 🔧 [P0-CRITICAL修复] 查询结果中 grade_salary_2025 的前10个值: [3641.0, 3641.0, 3641.0, 3641.0, 3641.0, 3641.0, 3641.0, 3641.0, 3641.0, 3641.0]
2025-07-21 11:59:21.193 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:569 | 成功从表 salary_data_2025_07_active_employees 获取第3页数据（含排序）: 50 行，总计1396行
2025-07-21 11:59:21.194 | INFO     | src.core.unified_data_request_manager:request_table_data:249 | 数据请求处理完成: 28字段, 50行, 耗时16.0ms
2025-07-21 11:59:21.195 | INFO     | src.services.table_data_service:load_table_data:347 | 🎯 [统一格式化] 数据格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:21.196 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:2956 | 🆕 接收到新架构数据更新事件: salary_data_2025_07_active_employees
2025-07-21 11:59:21.197 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:2981 | 数据内容: 50行 x 28列
2025-07-21 11:59:21.198 | INFO     | src.gui.prototype.prototype_main_window:set_data:685 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-21 11:59:21.199 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:1164 | 过滤了 4 个系统字段
2025-07-21 11:59:21.200 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5147 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 11:59:21.205 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 11:59:21.205 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 11:59:21.208 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:21.209 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 11:59:21.210 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 11:59:21.210 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 11:59:21.211 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 11:59:21.213 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 11:59:21.214 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 11:59:21.215 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 11:59:21.216 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 11:59:21.217 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 11:59:21.218 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 11:59:21.218 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 11:59:21.219 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 11:59:21.220 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 11:59:21.220 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 11:59:21.229 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 25.1ms
2025-07-21 11:59:21.230 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 11:59:21.231 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 11:59:21.232 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 降序
2025-07-21 11:59:21.232 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 11:59:21.234 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 50
2025-07-21 11:59:21.235 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 11:59:21.236 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3007 | 🔧 [分页修复] 数据更新事件设置总记录数: 1396
2025-07-21 11:59:21.236 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3798 | 🔧 [紧急修复] 新架构分页: 第3页
2025-07-21 11:59:21.237 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3810 | 新架构分页时应用排序: 1列
2025-07-21 11:59:21.238 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 3
2025-07-21 11:59:21.240 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5147 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 11:59:21.242 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 11:59:21.245 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 11:59:21.248 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:21.249 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 11:59:21.250 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 11:59:21.250 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 11:59:21.251 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 11:59:21.252 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 11:59:21.252 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 11:59:21.253 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 11:59:21.254 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 11:59:21.256 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 11:59:21.257 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 11:59:21.257 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 11:59:21.258 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 11:59:21.259 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 11:59:21.260 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 11:59:21.269 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 26.1ms
2025-07-21 11:59:21.269 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 11:59:21.270 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 11:59:21.271 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 降序
2025-07-21 11:59:21.272 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 11:59:21.272 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 11:59:21.273 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3873 | 🔧 [紧急修复] 新架构分页成功: 第3页, 50行
2025-07-21 11:59:21.275 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3798 | 🔧 [紧急修复] 新架构分页: 第3页
2025-07-21 11:59:21.276 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3810 | 新架构分页时应用排序: 1列
2025-07-21 11:59:21.277 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 3
2025-07-21 11:59:21.279 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5147 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 11:59:21.282 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 11:59:21.282 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 11:59:21.285 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:21.286 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 11:59:21.287 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 11:59:21.288 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 11:59:21.288 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 11:59:21.289 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 11:59:21.290 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 11:59:21.290 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 11:59:21.292 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 11:59:21.292 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 11:59:21.293 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 11:59:21.294 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 11:59:21.294 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 11:59:21.298 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 11:59:21.298 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 11:59:21.308 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 27.0ms
2025-07-21 11:59:21.308 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 11:59:21.309 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 11:59:21.310 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 降序
2025-07-21 11:59:21.311 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 11:59:21.311 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 11:59:21.312 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3873 | 🔧 [紧急修复] 新架构分页成功: 第3页, 50行
2025-07-21 11:59:21.313 | INFO     | src.gui.widgets.pagination_widget:set_current_page:330 | 页码切换到: 3
2025-07-21 11:59:21.313 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3012 | 🔧 [分页修复] 数据更新事件设置当前页: 3
2025-07-21 11:59:21.314 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3018 | 🔧 [分页修复] 分页状态: 当前页=3, 总页数=28, 总记录数=1396
2025-07-21 11:59:21.314 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3024 | 🔧 [分页修复] 按钮可用性: 下一页=True, 上一页=True
2025-07-21 11:59:21.319 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5147 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 11:59:21.321 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 11:59:21.322 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 11:59:21.325 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:21.326 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 11:59:21.328 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 11:59:21.329 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 11:59:21.329 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 11:59:21.330 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 11:59:21.331 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 11:59:21.331 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 11:59:21.333 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 11:59:21.333 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 11:59:21.334 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 11:59:21.335 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 11:59:21.335 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 11:59:21.338 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 11:59:21.339 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 11:59:21.348 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 28.1ms
2025-07-21 11:59:21.349 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 11:59:21.350 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 11:59:21.350 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 降序
2025-07-21 11:59:21.351 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 11:59:21.352 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 11:59:21.352 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3873 | 🔧 [紧急修复] 新架构分页成功: 第3页, 50行
2025-07-21 11:59:21.353 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3798 | 🔧 [紧急修复] 新架构分页: 第3页
2025-07-21 11:59:21.354 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3810 | 新架构分页时应用排序: 1列
2025-07-21 11:59:21.354 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 3
2025-07-21 11:59:21.359 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5147 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 11:59:21.361 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 11:59:21.362 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 11:59:21.365 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:21.367 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 11:59:21.368 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 11:59:21.368 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 11:59:21.369 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 11:59:21.370 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 11:59:21.370 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 11:59:21.371 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 11:59:21.372 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 11:59:21.373 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 11:59:21.374 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 11:59:21.374 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 11:59:21.377 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 11:59:21.378 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 11:59:21.379 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 11:59:21.388 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 28.1ms
2025-07-21 11:59:21.388 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 11:59:21.389 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 11:59:21.390 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 降序
2025-07-21 11:59:21.391 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 11:59:21.392 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 11:59:21.392 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3873 | 🔧 [紧急修复] 新架构分页成功: 第3页, 50行
2025-07-21 11:59:21.883 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1564 | 列宽设置已保存: salary_data_2025_07_active_employees (24 列)
2025-07-21 11:59:25.832 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3798 | 🔧 [紧急修复] 新架构分页: 第4页
2025-07-21 11:59:25.833 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3810 | 新架构分页时应用排序: 1列
2025-07-21 11:59:25.834 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 4
2025-07-21 11:59:25.835 | INFO     | src.core.unified_data_request_manager:request_table_data:200 | 开始处理数据请求: salary_data_2025_07_active_employees, 类型: initial_load
2025-07-21 11:59:25.835 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 11:59:25.836 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 11:59:25.837 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_07_active_employees 分页获取数据（支持排序）: 第4页, 每页50条, 排序=1列
2025-07-21 11:59:25.838 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:516 | 🔧 [排序修复] 准备排序列: grade_salary_2025
2025-07-21 11:59:25.838 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 11:59:25.839 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:523 | 🔧 [排序修复] 列 'grade_salary_2025' 验证通过，开始排序
2025-07-21 11:59:25.840 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:532 | 🔧 [排序修复] 数值列排序: grade_salary_2025 DESC
2025-07-21 11:59:25.840 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:539 | 🔧 [排序修复] 添加排序子句: CAST("grade_salary_2025" AS REAL) DESC
2025-07-21 11:59:25.843 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | 🔧 [P0-CRITICAL修复] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_07_active_employees" ORDER BY CAST("grade_salary_2025" AS REAL) DESC LIMIT 50 OFFSET 150
2025-07-21 11:59:25.846 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:557 | 🔧 [P0-CRITICAL修复] 数据库查询返回的前5个工号: ['19930690.0', '19950397.0', '19940223.0', '19931136.0', '19941019.0']
2025-07-21 11:59:25.848 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:566 | 🔧 [P0-CRITICAL修复] 查询结果中 grade_salary_2025 的前10个值: [3391.0, 3391.0, 3391.0, 3391.0, 3391.0, 3391.0, 3391.0, 3391.0, 3391.0, 3391.0]
2025-07-21 11:59:25.849 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:569 | 成功从表 salary_data_2025_07_active_employees 获取第4页数据（含排序）: 50 行，总计1396行
2025-07-21 11:59:25.850 | INFO     | src.core.unified_data_request_manager:request_table_data:249 | 数据请求处理完成: 28字段, 50行, 耗时15.0ms
2025-07-21 11:59:25.851 | INFO     | src.services.table_data_service:load_table_data:347 | 🎯 [统一格式化] 数据格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:25.852 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:2956 | 🆕 接收到新架构数据更新事件: salary_data_2025_07_active_employees
2025-07-21 11:59:25.855 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:2981 | 数据内容: 50行 x 28列
2025-07-21 11:59:25.856 | INFO     | src.gui.prototype.prototype_main_window:set_data:685 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-21 11:59:25.857 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:1164 | 过滤了 4 个系统字段
2025-07-21 11:59:25.858 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5147 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 11:59:25.861 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 11:59:25.862 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 11:59:25.866 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:25.867 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 11:59:25.867 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 11:59:25.868 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 11:59:25.869 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 11:59:25.869 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 11:59:25.870 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 11:59:25.871 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 11:59:25.872 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 11:59:25.875 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 11:59:25.875 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 11:59:25.876 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 11:59:25.877 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 11:59:25.878 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 11:59:25.878 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 11:59:25.887 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 26.8ms
2025-07-21 11:59:25.888 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 11:59:25.888 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 11:59:25.889 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 降序
2025-07-21 11:59:25.897 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 11:59:25.898 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 50
2025-07-21 11:59:25.898 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3798 | 🔧 [紧急修复] 新架构分页: 第1页
2025-07-21 11:59:25.899 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3810 | 新架构分页时应用排序: 1列
2025-07-21 11:59:25.900 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 1
2025-07-21 11:59:25.904 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5147 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 11:59:25.907 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 11:59:25.907 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 11:59:25.910 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:25.911 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 11:59:25.913 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 11:59:25.914 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 11:59:25.915 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 11:59:25.915 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 11:59:25.916 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 11:59:25.917 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 11:59:25.919 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 11:59:25.920 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 11:59:25.920 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 11:59:25.921 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 11:59:25.922 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 11:59:25.922 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 11:59:25.926 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 11:59:25.935 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 28.4ms
2025-07-21 11:59:25.935 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 11:59:25.936 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 11:59:25.936 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 降序
2025-07-21 11:59:25.937 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 11:59:25.938 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 11:59:25.939 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3873 | 🔧 [紧急修复] 新架构分页成功: 第1页, 50行
2025-07-21 11:59:25.939 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3798 | 🔧 [紧急修复] 新架构分页: 第1页
2025-07-21 11:59:25.940 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3810 | 新架构分页时应用排序: 1列
2025-07-21 11:59:25.941 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 1
2025-07-21 11:59:25.945 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5147 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 11:59:25.948 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 11:59:25.948 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 11:59:25.952 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:25.952 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 11:59:25.953 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 11:59:25.955 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 11:59:25.956 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 11:59:25.957 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 11:59:25.957 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 11:59:25.958 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 11:59:25.960 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 11:59:25.960 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 11:59:25.961 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 11:59:25.962 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 11:59:25.962 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 11:59:25.963 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 11:59:25.966 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 11:59:25.975 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 28.3ms
2025-07-21 11:59:25.976 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 11:59:25.976 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 11:59:25.977 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 降序
2025-07-21 11:59:25.978 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 11:59:25.979 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 11:59:25.979 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3873 | 🔧 [紧急修复] 新架构分页成功: 第1页, 50行
2025-07-21 11:59:25.980 | INFO     | src.gui.widgets.pagination_widget:set_current_page:330 | 页码切换到: 1
2025-07-21 11:59:25.981 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3798 | 🔧 [紧急修复] 新架构分页: 第4页
2025-07-21 11:59:25.981 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3810 | 新架构分页时应用排序: 1列
2025-07-21 11:59:25.982 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 4
2025-07-21 11:59:25.986 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5147 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 11:59:25.989 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 11:59:25.989 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 11:59:25.992 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:25.995 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 11:59:25.996 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 11:59:25.996 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 11:59:25.997 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 11:59:25.998 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 11:59:25.998 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 11:59:25.999 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 11:59:26.000 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 11:59:26.001 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 11:59:26.002 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 11:59:26.002 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 11:59:26.005 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 11:59:26.006 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 11:59:26.007 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 11:59:26.016 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 28.1ms
2025-07-21 11:59:26.017 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 11:59:26.018 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 11:59:26.018 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 降序
2025-07-21 11:59:26.019 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 11:59:26.020 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 11:59:26.020 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3873 | 🔧 [紧急修复] 新架构分页成功: 第4页, 50行
2025-07-21 11:59:26.021 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3798 | 🔧 [紧急修复] 新架构分页: 第4页
2025-07-21 11:59:26.022 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3810 | 新架构分页时应用排序: 1列
2025-07-21 11:59:26.023 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 4
2025-07-21 11:59:26.027 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5147 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 11:59:26.030 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 11:59:26.030 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 11:59:26.033 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:26.036 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 11:59:26.037 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 11:59:26.037 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 11:59:26.038 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 11:59:26.038 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 11:59:26.039 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 11:59:26.040 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 11:59:26.041 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 11:59:26.042 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 11:59:26.042 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 11:59:26.043 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 11:59:26.044 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 11:59:26.045 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 11:59:26.048 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 11:59:26.057 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 27.6ms
2025-07-21 11:59:26.058 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 11:59:26.059 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 11:59:26.059 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 降序
2025-07-21 11:59:26.060 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 11:59:26.061 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 11:59:26.062 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3873 | 🔧 [紧急修复] 新架构分页成功: 第4页, 50行
2025-07-21 11:59:26.062 | INFO     | src.gui.widgets.pagination_widget:set_current_page:330 | 页码切换到: 4
2025-07-21 11:59:26.063 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3012 | 🔧 [分页修复] 数据更新事件设置当前页: 4
2025-07-21 11:59:26.063 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3018 | 🔧 [分页修复] 分页状态: 当前页=4, 总页数=28, 总记录数=1396
2025-07-21 11:59:26.064 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3024 | 🔧 [分页修复] 按钮可用性: 下一页=True, 上一页=True
2025-07-21 11:59:26.068 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5147 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 11:59:26.071 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 11:59:26.072 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 11:59:26.075 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:26.075 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 11:59:26.078 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 11:59:26.079 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 11:59:26.079 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 11:59:26.080 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 11:59:26.081 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 11:59:26.081 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 11:59:26.083 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 11:59:26.084 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 11:59:26.084 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 11:59:26.085 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 11:59:26.086 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 11:59:26.086 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 11:59:26.089 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 11:59:26.098 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 28.1ms
2025-07-21 11:59:26.099 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 11:59:26.100 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 11:59:26.100 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 降序
2025-07-21 11:59:26.101 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 11:59:26.102 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 11:59:26.103 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3873 | 🔧 [紧急修复] 新架构分页成功: 第4页, 50行
2025-07-21 11:59:26.103 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3798 | 🔧 [紧急修复] 新架构分页: 第4页
2025-07-21 11:59:26.104 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3810 | 新架构分页时应用排序: 1列
2025-07-21 11:59:26.105 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 4
2025-07-21 11:59:26.109 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5147 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 11:59:26.112 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 11:59:26.113 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 11:59:26.116 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:26.116 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 11:59:26.117 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 11:59:26.119 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 11:59:26.120 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 11:59:26.121 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 11:59:26.121 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 11:59:26.122 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 11:59:26.123 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 11:59:26.125 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 11:59:26.125 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 11:59:26.126 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 11:59:26.127 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 11:59:26.128 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 11:59:26.130 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 11:59:26.139 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 28.1ms
2025-07-21 11:59:26.140 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 11:59:26.141 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 11:59:26.141 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 降序
2025-07-21 11:59:26.142 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 11:59:26.143 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 11:59:26.144 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3873 | 🔧 [紧急修复] 新架构分页成功: 第4页, 50行
2025-07-21 11:59:26.144 | INFO     | src.gui.widgets.pagination_widget:set_current_page:330 | 页码切换到: 4
2025-07-21 11:59:26.633 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1564 | 列宽设置已保存: salary_data_2025_07_active_employees (24 列)
2025-07-21 11:59:28.941 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3798 | 🔧 [紧急修复] 新架构分页: 第5页
2025-07-21 11:59:28.943 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3810 | 新架构分页时应用排序: 1列
2025-07-21 11:59:28.944 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 5
2025-07-21 11:59:28.945 | INFO     | src.core.unified_data_request_manager:request_table_data:200 | 开始处理数据请求: salary_data_2025_07_active_employees, 类型: initial_load
2025-07-21 11:59:28.946 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 11:59:28.947 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 11:59:28.947 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_07_active_employees 分页获取数据（支持排序）: 第5页, 每页50条, 排序=1列
2025-07-21 11:59:28.948 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:516 | 🔧 [排序修复] 准备排序列: grade_salary_2025
2025-07-21 11:59:28.949 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 11:59:28.950 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:523 | 🔧 [排序修复] 列 'grade_salary_2025' 验证通过，开始排序
2025-07-21 11:59:28.954 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:532 | 🔧 [排序修复] 数值列排序: grade_salary_2025 DESC
2025-07-21 11:59:28.955 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:539 | 🔧 [排序修复] 添加排序子句: CAST("grade_salary_2025" AS REAL) DESC
2025-07-21 11:59:28.956 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | 🔧 [P0-CRITICAL修复] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_07_active_employees" ORDER BY CAST("grade_salary_2025" AS REAL) DESC LIMIT 50 OFFSET 200
2025-07-21 11:59:28.961 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:557 | 🔧 [P0-CRITICAL修复] 数据库查询返回的前5个工号: ['19940354.0', '19930924.0', '19890672.0', '19950361.0', '19950369.0']
2025-07-21 11:59:28.962 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:566 | 🔧 [P0-CRITICAL修复] 查询结果中 grade_salary_2025 的前10个值: [3266.0, 3266.0, 3266.0, 3266.0, 3266.0, 3266.0, 3266.0, 3266.0, 3266.0, 3141.0]
2025-07-21 11:59:28.965 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:569 | 成功从表 salary_data_2025_07_active_employees 获取第5页数据（含排序）: 50 行，总计1396行
2025-07-21 11:59:28.966 | INFO     | src.core.unified_data_request_manager:request_table_data:249 | 数据请求处理完成: 28字段, 50行, 耗时20.9ms
2025-07-21 11:59:28.968 | INFO     | src.services.table_data_service:load_table_data:347 | 🎯 [统一格式化] 数据格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:28.969 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:2956 | 🆕 接收到新架构数据更新事件: salary_data_2025_07_active_employees
2025-07-21 11:59:28.970 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:2981 | 数据内容: 50行 x 28列
2025-07-21 11:59:28.971 | INFO     | src.gui.prototype.prototype_main_window:set_data:685 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-21 11:59:28.972 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:1164 | 过滤了 4 个系统字段
2025-07-21 11:59:28.976 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5147 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 11:59:28.980 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 11:59:28.981 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 11:59:28.984 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:28.985 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 11:59:28.986 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 11:59:28.987 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 11:59:28.987 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 11:59:28.988 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 11:59:28.989 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 11:59:28.989 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 11:59:28.991 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 11:59:28.991 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 11:59:28.992 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 11:59:28.993 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 11:59:28.994 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 11:59:28.997 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 11:59:28.997 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 11:59:29.006 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 27.2ms
2025-07-21 11:59:29.007 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 11:59:29.008 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 11:59:29.009 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 降序
2025-07-21 11:59:29.016 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 11:59:29.017 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 50
2025-07-21 11:59:29.018 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3798 | 🔧 [紧急修复] 新架构分页: 第1页
2025-07-21 11:59:29.019 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3810 | 新架构分页时应用排序: 1列
2025-07-21 11:59:29.019 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 1
2025-07-21 11:59:29.022 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5147 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 11:59:29.027 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 11:59:29.027 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 11:59:29.030 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:29.031 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 11:59:29.032 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 11:59:29.032 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 11:59:29.033 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 11:59:29.034 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 11:59:29.034 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 11:59:29.037 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 11:59:29.038 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 11:59:29.039 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 11:59:29.040 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 11:59:29.040 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 11:59:29.041 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 11:59:29.042 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 11:59:29.043 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 11:59:29.053 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 26.1ms
2025-07-21 11:59:29.053 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 11:59:29.054 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 11:59:29.055 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 降序
2025-07-21 11:59:29.056 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 11:59:29.057 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 11:59:29.058 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3873 | 🔧 [紧急修复] 新架构分页成功: 第1页, 50行
2025-07-21 11:59:29.059 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3798 | 🔧 [紧急修复] 新架构分页: 第1页
2025-07-21 11:59:29.059 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3810 | 新架构分页时应用排序: 1列
2025-07-21 11:59:29.060 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 1
2025-07-21 11:59:29.062 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5147 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 11:59:29.065 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 11:59:29.065 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 11:59:29.071 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:29.072 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 11:59:29.072 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 11:59:29.073 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 11:59:29.074 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 11:59:29.074 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 11:59:29.075 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 11:59:29.076 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 11:59:29.077 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 11:59:29.079 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 11:59:29.080 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 11:59:29.081 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 11:59:29.081 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 11:59:29.082 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 11:59:29.083 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 11:59:29.092 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 28.0ms
2025-07-21 11:59:29.093 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 11:59:29.094 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 11:59:29.095 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 降序
2025-07-21 11:59:29.096 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 11:59:29.096 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 11:59:29.097 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3873 | 🔧 [紧急修复] 新架构分页成功: 第1页, 50行
2025-07-21 11:59:29.099 | INFO     | src.gui.widgets.pagination_widget:set_current_page:330 | 页码切换到: 1
2025-07-21 11:59:29.100 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3798 | 🔧 [紧急修复] 新架构分页: 第5页
2025-07-21 11:59:29.101 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3810 | 新架构分页时应用排序: 1列
2025-07-21 11:59:29.101 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 5
2025-07-21 11:59:29.103 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5147 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 11:59:29.106 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 11:59:29.106 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 11:59:29.109 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:29.111 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 11:59:29.112 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 11:59:29.112 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 11:59:29.113 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 11:59:29.114 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 11:59:29.114 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 11:59:29.115 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 11:59:29.117 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 11:59:29.117 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 11:59:29.118 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 11:59:29.118 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 11:59:29.121 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 11:59:29.122 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 11:59:29.123 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 11:59:29.132 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 27.1ms
2025-07-21 11:59:29.133 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 11:59:29.134 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 11:59:29.134 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 降序
2025-07-21 11:59:29.135 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 11:59:29.136 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 11:59:29.137 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3873 | 🔧 [紧急修复] 新架构分页成功: 第5页, 50行
2025-07-21 11:59:29.137 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3798 | 🔧 [紧急修复] 新架构分页: 第5页
2025-07-21 11:59:29.138 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3810 | 新架构分页时应用排序: 1列
2025-07-21 11:59:29.138 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 5
2025-07-21 11:59:29.143 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5147 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 11:59:29.145 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 11:59:29.146 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 11:59:29.149 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:29.150 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 11:59:29.150 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 11:59:29.153 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 11:59:29.154 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 11:59:29.154 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 11:59:29.155 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 11:59:29.156 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 11:59:29.157 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 11:59:29.158 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 11:59:29.158 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 11:59:29.159 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 11:59:29.160 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 11:59:29.161 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 11:59:29.163 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 11:59:29.172 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 28.1ms
2025-07-21 11:59:29.173 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 11:59:29.174 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 11:59:29.175 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 降序
2025-07-21 11:59:29.176 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 11:59:29.176 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 11:59:29.177 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3873 | 🔧 [紧急修复] 新架构分页成功: 第5页, 50行
2025-07-21 11:59:29.178 | INFO     | src.gui.widgets.pagination_widget:set_current_page:330 | 页码切换到: 5
2025-07-21 11:59:29.178 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3012 | 🔧 [分页修复] 数据更新事件设置当前页: 5
2025-07-21 11:59:29.179 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3018 | 🔧 [分页修复] 分页状态: 当前页=5, 总页数=28, 总记录数=1396
2025-07-21 11:59:29.179 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3024 | 🔧 [分页修复] 按钮可用性: 下一页=True, 上一页=True
2025-07-21 11:59:29.184 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5147 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 11:59:29.187 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 11:59:29.188 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 11:59:29.190 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:29.191 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 11:59:29.194 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 11:59:29.194 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 11:59:29.195 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 11:59:29.196 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 11:59:29.196 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 11:59:29.197 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 11:59:29.199 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 11:59:29.199 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 11:59:29.200 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 11:59:29.201 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 11:59:29.201 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 11:59:29.204 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 11:59:29.205 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 11:59:29.214 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 28.1ms
2025-07-21 11:59:29.215 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 11:59:29.215 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 11:59:29.216 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 降序
2025-07-21 11:59:29.217 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 11:59:29.218 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 11:59:29.218 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3873 | 🔧 [紧急修复] 新架构分页成功: 第5页, 50行
2025-07-21 11:59:29.219 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3798 | 🔧 [紧急修复] 新架构分页: 第5页
2025-07-21 11:59:29.220 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3810 | 新架构分页时应用排序: 1列
2025-07-21 11:59:29.220 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 5
2025-07-21 11:59:29.224 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5147 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 11:59:29.227 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 11:59:29.227 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 11:59:29.230 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 11:59:29.231 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 11:59:29.232 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 11:59:29.232 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 11:59:29.235 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 11:59:29.235 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 11:59:29.236 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 11:59:29.237 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 11:59:29.238 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 11:59:29.239 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 11:59:29.240 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 11:59:29.240 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 11:59:29.241 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 11:59:29.242 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 11:59:29.243 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 11:59:29.253 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 27.1ms
2025-07-21 11:59:29.255 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 11:59:29.255 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 11:59:29.256 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 降序
2025-07-21 11:59:29.257 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 11:59:29.258 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 11:59:29.258 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3873 | 🔧 [紧急修复] 新架构分页成功: 第5页, 50行
2025-07-21 11:59:29.259 | INFO     | src.gui.widgets.pagination_widget:set_current_page:330 | 页码切换到: 5
2025-07-21 11:59:29.798 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1564 | 列宽设置已保存: salary_data_2025_07_active_employees (24 列)
2025-07-21 11:59:33.046 | INFO     | __main__:main:427 | 应用程序正常退出
