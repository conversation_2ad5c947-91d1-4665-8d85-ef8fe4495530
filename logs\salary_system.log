2025-07-21 12:22:43.516 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-07-21 12:22:43.516 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-07-21 12:22:43.516 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-07-21 12:22:43.516 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-07-21 12:22:43.516 | INFO     | src.utils.log_config:_log_initialization_info:214 | 日志文件路径: logs/salary_system.log
2025-07-21 12:22:43.516 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-07-21 12:22:45.743 | INFO     | __main__:setup_qt_exception_handling:218 | 🔧 [P0-修复] PyQt专用异常处理机制已启用
2025-07-21 12:22:45.759 | INFO     | __main__:setup_app_logging:331 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-07-21 12:22:45.759 | INFO     | __main__:main:395 | 初始化核心管理器...
2025-07-21 12:22:45.759 | INFO     | src.modules.system_config.config_manager:__init__:313 | 配置管理器初始化完成，配置文件: config.json
2025-07-21 12:22:45.759 | INFO     | src.modules.system_config.config_manager:load_config:340 | 正在加载配置文件: config.json
2025-07-21 12:22:45.759 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-07-21 12:22:45.759 | INFO     | src.modules.system_config.config_manager:load_config:352 | 配置文件加载成功
2025-07-21 12:22:45.759 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-07-21 12:22:45.790 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-07-21 12:22:45.806 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-07-21 12:22:45.806 | INFO     | src.modules.system_config.config_manager:__init__:313 | 配置管理器初始化完成，配置文件: config.json
2025-07-21 12:22:45.806 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:104 | 动态表管理器初始化完成
2025-07-21 12:22:45.806 | INFO     | __main__:main:400 | 核心管理器初始化完成。
2025-07-21 12:22:45.806 | INFO     | src.core.data_source_unification_manager:__init__:128 | 数据源统一管理器初始化完成
2025-07-21 12:22:45.806 | INFO     | src.core.table_sort_state_manager:__init__:174 | 表级排序状态管理器初始化完成
2025-07-21 12:22:45.853 | INFO     | src.core.architecture_factory:__init__:62 | 架构重构工厂初始化完成
2025-07-21 12:22:45.853 | INFO     | src.core.architecture_factory:initialize_architecture:72 | 开始初始化架构重构系统...
2025-07-21 12:22:45.964 | WARNING  | src.modules.data_import.config_sync_manager:_do_config_initialization:139 | 创建了最小默认配置，可能需要重新导入数据以生成字段映射
2025-07-21 12:22:45.964 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-07-21 12:22:45.980 | INFO     | src.core.event_bus:__init__:227 | 事件总线初始化完成
2025-07-21 12:22:45.980 | INFO     | src.core.unified_state_manager:_load_state:465 | 状态文件不存在，使用默认状态
2025-07-21 12:22:45.980 | INFO     | src.core.unified_state_manager:__init__:177 | 统一状态管理器初始化完成
2025-07-21 12:22:45.980 | INFO     | src.core.unified_data_request_manager:__init__:187 | 统一数据请求管理器初始化完成
2025-07-21 12:22:45.980 | INFO     | src.core.unified_data_request_manager:__init__:187 | 统一数据请求管理器初始化完成
2025-07-21 12:22:45.980 | INFO     | src.core.unified_state_manager:_load_state:465 | 状态文件不存在，使用默认状态
2025-07-21 12:22:45.980 | INFO     | src.core.unified_state_manager:__init__:177 | 统一状态管理器初始化完成
2025-07-21 12:22:45.980 | INFO     | src.services.table_data_service:__init__:73 | 表格数据服务初始化完成
2025-07-21 12:22:45.980 | INFO     | src.core.architecture_factory:initialize_architecture:103 | 🎉 架构重构系统初始化成功！耗时: 127.3ms
2025-07-21 12:22:45.996 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:46 | 数据预加载缓存初始化完成 - 最大条目数: 100, TTL: 300秒
2025-07-21 12:22:45.996 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:232 | 表格状态缓存初始化完成
2025-07-21 12:22:45.996 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:32 | 表头配置缓存初始化完成
2025-07-21 12:22:45.996 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:211 | 字段映射缓存初始化完成
2025-07-21 12:22:45.996 | INFO     | src.gui.prototype.performance.performance_manager:__init__:46 | 🚀 性能管理器初始化完成
2025-07-21 12:22:46.011 | INFO     | src.gui.prototype.prototype_main_window:__init__:2809 | 🚀 性能管理器已集成
2025-07-21 12:22:46.011 | INFO     | src.gui.prototype.prototype_main_window:__init__:2811 | ✅ 新架构集成成功！
2025-07-21 12:22:46.011 | INFO     | src.gui.prototype.prototype_main_window:_inject_config_sync_manager_to_existing_tables:2922 | ConfigSyncManager重新注入完成，已更新0个表格实例
2025-07-21 12:22:46.011 | INFO     | src.gui.prototype.prototype_main_window:_setup_new_architecture_listeners:2888 | ✅ 新架构事件监听器设置完成
2025-07-21 12:22:46.011 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-07-21 12:22:46.011 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-07-21 12:22:46.011 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-07-21 12:22:46.246 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:2091 | 菜单栏创建完成
2025-07-21 12:22:46.246 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-07-21 12:22:46.262 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: user_preferences.json
2025-07-21 12:22:46.262 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-07-21 12:22:46.262 | INFO     | src.gui.prototype.prototype_main_window:__init__:2067 | 菜单栏管理器初始化完成
2025-07-21 12:22:46.262 | INFO     | src.gui.table_header_manager:__init__:68 | 表头管理器初始化完成
2025-07-21 12:22:46.262 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:4044 | 管理器设置完成，包含增强版表头管理器
2025-07-21 12:22:46.262 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-07-21 12:22:46.277 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:148 | 智能树形展开算法初始化完成
2025-07-21 12:22:46.293 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:1042 | 开始从元数据动态加载工资数据...
2025-07-21 12:22:46.293 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:1049 | 检测到首次启动，将延迟加载导航数据确保数据库就绪
2025-07-21 12:22:46.305 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:680 | 导航面板已重构：移除功能性导航，专注数据导航
2025-07-21 12:22:46.319 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:715 | 恢复导航状态: 0个展开项
2025-07-21 12:22:46.320 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表', '工资表 > 2025年']
2025-07-21 12:22:46.321 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:521 | 增强导航面板初始化完成
2025-07-21 12:22:46.357 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1328 | 快捷键注册完成: 18/18 个
2025-07-21 12:22:46.357 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1673 | 拖拽排序管理器初始化完成
2025-07-21 12:22:46.363 | INFO     | src.modules.data_import.header_edit_manager:__init__:82 | 表头编辑管理器初始化完成
2025-07-21 12:22:46.364 | INFO     | src.core.unified_state_manager:_load_state:465 | 状态文件不存在，使用默认状态
2025-07-21 12:22:46.365 | INFO     | src.core.unified_state_manager:__init__:177 | 统一状态管理器初始化完成
2025-07-21 12:22:46.366 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2060 | 🧹 [代码清理] 格式管理器已迁移到MasterFormatManager
2025-07-21 12:22:46.376 | INFO     | src.gui.prototype.widgets.column_sort_manager:_load_field_mappings_from_config:338 | 🔧 [新架构] 成功加载 0 个字段映射
2025-07-21 12:22:46.379 | INFO     | src.gui.prototype.widgets.column_sort_manager:__init__:99 | 🆕 [新架构排序] 多列排序管理器初始化完成，最大排序列数: 3
2025-07-21 12:22:46.380 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2102 | 🆕 [新架构多列排序] 初始化完成，支持最多3列排序
2025-07-21 12:22:46.382 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1507 | 列宽管理器初始化完成
2025-07-21 12:22:46.382 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2109 | 列宽管理器初始化完成
2025-07-21 12:22:46.386 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2123 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-07-21 12:22:46.387 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3982 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-07-21 12:22:46.389 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 0 行, 7 列
2025-07-21 12:22:46.403 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 0 行, 耗时: 16.6ms
2025-07-21 12:22:46.404 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 12:22:46.405 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6315 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-07-21 12:22:46.405 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1578 | 🆕 [列宽保存] 配置文件不存在，使用默认列宽
2025-07-21 12:22:46.406 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1652 | 表格已初始化为空白状态，等待用户导入数据
2025-07-21 12:22:46.412 | INFO     | src.gui.widgets.pagination_widget:__init__:165 | 分页组件初始化完成
2025-07-21 12:22:46.450 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:517 | 控制面板按钮信号连接完成
2025-07-21 12:22:46.466 | INFO     | src.modules.system_config.config_manager:__init__:313 | 配置管理器初始化完成，配置文件: config.json
2025-07-21 12:22:46.467 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-07-21 12:22:46.470 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:4017 | 快捷键设置完成
2025-07-21 12:22:46.471 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:4006 | 主窗口UI设置完成。
2025-07-21 12:22:46.472 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:4055 | 🔧 [全局排序] 全局排序开关连接成功
2025-07-21 12:22:46.473 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:4083 | 🆕 [新架构排序] 使用表格组件内部的自定义排序循环，无需连接排序信号
2025-07-21 12:22:46.474 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:4092 | ✅ 已连接分页组件事件到新架构
2025-07-21 12:22:46.474 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:4094 | 信号连接设置完成
2025-07-21 12:22:46.475 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:4875 | 已加载字段映射信息，共0个表的映射
2025-07-21 12:22:46.476 | WARNING  | src.gui.prototype.prototype_main_window:set_data:645 | 尝试设置空数据，将显示提示信息。
2025-07-21 12:22:46.478 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3982 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-07-21 12:22:46.481 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 0 行, 7 列
2025-07-21 12:22:46.495 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 0 行, 耗时: 17.2ms
2025-07-21 12:22:46.496 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 12:22:46.497 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6315 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-07-21 12:22:46.498 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1578 | 🆕 [列宽保存] 配置文件不存在，使用默认列宽
2025-07-21 12:22:46.499 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1652 | 表格已初始化为空白状态，等待用户导入数据
2025-07-21 12:22:46.499 | INFO     | src.gui.widgets.pagination_widget:reset:354 | 分页状态已重置
2025-07-21 12:22:46.500 | WARNING  | src.gui.prototype.prototype_main_window:set_data:645 | 尝试设置空数据，将显示提示信息。
2025-07-21 12:22:46.501 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:3982 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-07-21 12:22:46.501 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 0 行, 7 列
2025-07-21 12:22:46.514 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 0 行, 耗时: 13.6ms
2025-07-21 12:22:46.514 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 12:22:46.515 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6315 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-07-21 12:22:46.516 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1578 | 🆕 [列宽保存] 配置文件不存在，使用默认列宽
2025-07-21 12:22:46.518 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1652 | 表格已初始化为空白状态，等待用户导入数据
2025-07-21 12:22:46.518 | INFO     | src.gui.widgets.pagination_widget:reset:354 | 分页状态已重置
2025-07-21 12:22:46.519 | INFO     | src.gui.prototype.prototype_main_window:__init__:2862 | 原型主窗口初始化完成
2025-07-21 12:22:46.818 | INFO     | __main__:main:422 | 应用程序启动成功
2025-07-21 12:22:46.828 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1266px)
2025-07-21 12:22:46.828 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:1800 | MainWorkspaceArea 响应式适配: sm
2025-07-21 12:22:46.831 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1003 | 执行延迟的自动选择最新数据...
2025-07-21 12:22:46.832 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1014 | 导航树即将刷新，跳过当前自动选择，将在刷新后执行
2025-07-21 12:22:47.093 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_load_salary_data:1064 | 执行延迟的工资数据加载...
2025-07-21 12:22:47.093 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1230 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-07-21 12:22:47.093 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1238 | 找到工资表节点: 📊 工资表
2025-07-21 12:22:47.093 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:885 | 找到 0 个匹配类型 'salary_data' 的表
2025-07-21 12:22:47.093 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:976 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-07-21 12:22:47.093 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:888 | 找到 3 个总表
2025-07-21 12:22:47.093 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1290 | 检测到数据库中没有工资数据表，直接使用兜底数据
2025-07-21 12:22:47.093 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_load_fallback_salary_data:1187 | 使用兜底数据加载导航
2025-07-21 12:22:47.294 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1093 | 导航树刷新完成，重新执行自动选择...
2025-07-21 12:22:47.294 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1036 | 开始获取最新工资数据路径...
2025-07-21 12:22:47.294 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:885 | 找到 0 个匹配类型 'salary_data' 的表
2025-07-21 12:22:47.294 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1041 | 未找到任何工资数据表
2025-07-21 12:22:47.294 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1098 | 未找到最新工资数据路径
2025-07-21 12:22:47.388 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1564 | 列宽设置已保存: default_table (7 列)
2025-07-21 12:22:51.857 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1564 | 列宽设置已保存: default_table (7 列)
2025-07-21 12:26:51.190 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:534 | 数据导入功能被触发，发出 import_requested 信号。
2025-07-21 12:26:51.206 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:4326 | 接收到数据导入请求，推断的目标路径: 工资表 > 2025年 > 07月 > 全部在职人员。打开导入对话框。
2025-07-21 12:26:51.206 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-07-21 12:26:51.206 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:72 | 多Sheet导入器初始化完成
2025-07-21 12:26:51.206 | INFO     | src.gui.widgets.target_selection_widget:_save_config:285 | 配置保存成功
2025-07-21 12:26:51.237 | INFO     | src.gui.widgets.target_selection_widget:set_target_from_path:505 | 从路径设置目标: 工资表 > 2025年 > 07月 > 全部在职人员
2025-07-21 12:26:51.237 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:885 | 找到 0 个匹配类型 'salary_data' 的表
2025-07-21 12:26:51.288 | INFO     | src.gui.main_dialogs:_get_template_fields:1872 | 🔧 [P3-修复] ConfigSyncManager未初始化，使用默认字段模板
2025-07-21 12:26:51.288 | INFO     | src.gui.main_dialogs:_init_field_mapping:1859 | 🔧 [P3-修复] 未找到字段模板，使用内置默认字段列表
2025-07-21 12:26:51.331 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-07-21 12:26:51.347 | INFO     | src.gui.main_dialogs:_apply_default_settings:2210 | 已应用默认设置: {'start_row': 1, 'import_mode': 'multi_sheet', 'auto_match_sheet': True, 'include_header': True, 'skip_empty_rows': True, 'create_table_mode': 'sheet_name', 'import_strategy': 'separate_tables', 'table_template': 'salary_data'}
2025-07-21 12:26:51.347 | INFO     | src.gui.main_dialogs:_setup_tooltips:2465 | 工具提示设置完成
2025-07-21 12:26:51.347 | INFO     | src.gui.main_dialogs:_setup_shortcuts:2504 | 快捷键设置完成
2025-07-21 12:26:51.347 | INFO     | src.gui.main_dialogs:__init__:77 | 数据导入对话框初始化完成。
2025-07-21 12:26:51.347 | INFO     | src.modules.data_import.multi_sheet_importer:set_config_sync_manager:81 | 🔧 [修复] ConfigSyncManager已设置到MultiSheetImporter
2025-07-21 12:26:51.347 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:4337 | ConfigSyncManager已设置到数据导入对话框
2025-07-21 12:26:59.737 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-21 12:27:01.353 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-21 12:27:01.356 | INFO     | src.modules.data_import.smart_sheet_matcher:record_user_choice:372 | 记录用户选择: 全部在职人员 -> 全部在职人员工资表
2025-07-21 12:27:01.358 | INFO     | src.gui.main_dialogs:_auto_select_sheet_by_category:2245 | 根据人员类别 '全部在职人员' 自动选择工作表: 全部在职人员工资表 (匹配类型: fuzzy, 得分: 0.60)
2025-07-21 12:27:03.692 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-21 12:27:03.879 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-21 12:27:03.895 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-21 12:27:03.895 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:211 | 开始导入多Sheet Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-07-21 12:27:03.895 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-21 12:27:04.098 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-21 12:27:04.098 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:222 | 检测到 4 个工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-21 12:27:04.114 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-21 12:27:04.114 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-07-21 12:27:04.114 | INFO     | src.utils.log_config:log_file_operation:252 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-07-21 12:27:04.223 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:320 | 🔧 [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-07-21 12:27:04.239 | INFO     | src.modules.data_import.excel_importer:_clean_data:427 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-07-21 12:27:04.239 | INFO     | src.modules.data_import.excel_importer:_clean_data:438 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-07-21 12:27:04.239 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:339 | 导入完成: 3行 x 16列
2025-07-21 12:27:04.239 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:567 | 数据质量检查: 已过滤1条姓名为空的记录
2025-07-21 12:27:04.239 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:608 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-07-21 12:27:04.239 | INFO     | src.modules.data_import.excel_importer:import_data:286 | 🔧 [修复标识] 数据导入最终完成: 2行 × 16列
2025-07-21 12:27:04.239 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:456 | 工作表 离休人员工资表 使用智能默认配置
2025-07-21 12:27:04.239 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:744 | 为Sheet '离休人员工资表' 生成智能默认配置: 1 个必需字段
2025-07-21 12:27:04.239 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:139 | 为模板 retired_employees 生成了 21 个字段映射
2025-07-21 12:27:04.239 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:473 | 使用专用模板 retired_employees 生成字段映射: 21 个字段
2025-07-21 12:27:04.254 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:615 | 完整字段映射保存成功: salary_data_2025_07_retired_employees
2025-07-21 12:27:04.270 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:520 | 为表 salary_data_2025_07_retired_employees 生成标准化字段映射: 21 个字段
2025-07-21 12:27:04.270 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:541 | Sheet 离休人员工资表 数据处理完成: 2 行
2025-07-21 12:27:04.270 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:371 | Sheet '离休人员工资表' 检测到模板类型: retired_employees
2025-07-21 12:27:04.286 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:700 | 成功创建表: salary_data_2025_07_retired_employees
2025-07-21 12:27:04.286 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:360 | 专用工资数据表创建成功: salary_data_2025_07_retired_employees (模板: retired_employees)
2025-07-21 12:27:04.348 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1135 | 🔧 [修复标识] 导入字段映射加载完成: 28 个映射规则
2025-07-21 12:27:04.348 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1143 | 🔧 [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-07-21 12:27:04.348 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1152 | 🔧 [修复标识] 导入列名映射成功: 18 个字段已映射
2025-07-21 12:27:04.364 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1289 | 成功向表 salary_data_2025_07_retired_employees 保存 2 条数据。
2025-07-21 12:27:04.379 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-21 12:27:04.379 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-07-21 12:27:04.379 | INFO     | src.utils.log_config:log_file_operation:252 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-07-21 12:27:04.489 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:320 | 🔧 [修复标识] Excel读取完成: 27列 (列过滤: 否)
2025-07-21 12:27:04.489 | INFO     | src.modules.data_import.excel_importer:_clean_data:427 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-07-21 12:27:04.489 | INFO     | src.modules.data_import.excel_importer:_clean_data:438 | 数据清理完成: 保留所有 27 列 (原始 27 列)
2025-07-21 12:27:04.489 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:339 | 导入完成: 14行 x 27列
2025-07-21 12:27:04.489 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:567 | 数据质量检查: 已过滤1条姓名为空的记录
2025-07-21 12:27:04.489 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:608 | 数据过滤完成: 原始14条记录，过滤1条无效记录，有效记录13条
2025-07-21 12:27:04.489 | INFO     | src.modules.data_import.excel_importer:import_data:286 | 🔧 [修复标识] 数据导入最终完成: 13行 × 27列
2025-07-21 12:27:04.489 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:456 | 工作表 退休人员工资表 使用智能默认配置
2025-07-21 12:27:04.489 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:744 | 为Sheet '退休人员工资表' 生成智能默认配置: 1 个必需字段
2025-07-21 12:27:04.489 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:139 | 为模板 pension_employees 生成了 32 个字段映射
2025-07-21 12:27:04.504 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:473 | 使用专用模板 pension_employees 生成字段映射: 32 个字段
2025-07-21 12:27:04.504 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:615 | 完整字段映射保存成功: salary_data_2025_07_pension_employees
2025-07-21 12:27:04.520 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:520 | 为表 salary_data_2025_07_pension_employees 生成标准化字段映射: 32 个字段
2025-07-21 12:27:04.520 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:541 | Sheet 退休人员工资表 数据处理完成: 13 行
2025-07-21 12:27:04.520 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:371 | Sheet '退休人员工资表' 检测到模板类型: pension_employees
2025-07-21 12:27:04.520 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:700 | 成功创建表: salary_data_2025_07_pension_employees
2025-07-21 12:27:04.520 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:360 | 专用工资数据表创建成功: salary_data_2025_07_pension_employees (模板: pension_employees)
2025-07-21 12:27:04.552 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1135 | 🔧 [修复标识] 导入字段映射加载完成: 32 个映射规则
2025-07-21 12:27:04.552 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1143 | 🔧 [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-07-21 12:27:04.552 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1152 | 🔧 [修复标识] 导入列名映射成功: 29 个字段已映射
2025-07-21 12:27:04.567 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1289 | 成功向表 salary_data_2025_07_pension_employees 保存 13 条数据。
2025-07-21 12:27:04.567 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-21 12:27:04.567 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-07-21 12:27:04.567 | INFO     | src.utils.log_config:log_file_operation:252 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-07-21 12:27:04.692 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:320 | 🔧 [修复标识] Excel读取完成: 23列 (列过滤: 否)
2025-07-21 12:27:04.708 | INFO     | src.modules.data_import.excel_importer:_clean_data:427 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-07-21 12:27:04.708 | INFO     | src.modules.data_import.excel_importer:_clean_data:438 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-07-21 12:27:04.708 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:339 | 导入完成: 1397行 x 23列
2025-07-21 12:27:04.708 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:567 | 数据质量检查: 已过滤1条姓名为空的记录
2025-07-21 12:27:04.708 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:608 | 数据过滤完成: 原始1397条记录，过滤1条无效记录，有效记录1396条
2025-07-21 12:27:04.723 | INFO     | src.modules.data_import.excel_importer:import_data:286 | 🔧 [修复标识] 数据导入最终完成: 1396行 × 23列
2025-07-21 12:27:04.723 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:456 | 工作表 全部在职人员工资表 使用智能默认配置
2025-07-21 12:27:04.723 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:744 | 为Sheet '全部在职人员工资表' 生成智能默认配置: 2 个必需字段
2025-07-21 12:27:04.739 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:139 | 为模板 active_employees 生成了 28 个字段映射
2025-07-21 12:27:04.739 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:473 | 使用专用模板 active_employees 生成字段映射: 28 个字段
2025-07-21 12:27:04.739 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:615 | 完整字段映射保存成功: salary_data_2025_07_active_employees
2025-07-21 12:27:04.739 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:520 | 为表 salary_data_2025_07_active_employees 生成标准化字段映射: 28 个字段
2025-07-21 12:27:04.739 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:541 | Sheet 全部在职人员工资表 数据处理完成: 1396 行
2025-07-21 12:27:04.739 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:371 | Sheet '全部在职人员工资表' 检测到模板类型: active_employees
2025-07-21 12:27:04.754 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:700 | 成功创建表: salary_data_2025_07_active_employees
2025-07-21 12:27:04.754 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:360 | 专用工资数据表创建成功: salary_data_2025_07_active_employees (模板: active_employees)
2025-07-21 12:27:04.770 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1135 | 🔧 [修复标识] 导入字段映射加载完成: 28 个映射规则
2025-07-21 12:27:04.770 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1143 | 🔧 [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-07-21 12:27:04.770 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1152 | 🔧 [修复标识] 导入列名映射成功: 25 个字段已映射
2025-07-21 12:27:04.801 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1289 | 成功向表 salary_data_2025_07_active_employees 保存 1396 条数据。
2025-07-21 12:27:04.801 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-21 12:27:04.801 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-07-21 12:27:04.801 | INFO     | src.utils.log_config:log_file_operation:252 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-07-21 12:27:04.911 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:320 | 🔧 [修复标识] Excel读取完成: 21列 (列过滤: 否)
2025-07-21 12:27:04.911 | INFO     | src.modules.data_import.excel_importer:_clean_data:427 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-07-21 12:27:04.911 | INFO     | src.modules.data_import.excel_importer:_clean_data:438 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-07-21 12:27:04.927 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:339 | 导入完成: 63行 x 21列
2025-07-21 12:27:04.927 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:567 | 数据质量检查: 已过滤1条姓名为空的记录
2025-07-21 12:27:04.927 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:608 | 数据过滤完成: 原始63条记录，过滤1条无效记录，有效记录62条
2025-07-21 12:27:04.927 | INFO     | src.modules.data_import.excel_importer:import_data:286 | 🔧 [修复标识] 数据导入最终完成: 62行 × 21列
2025-07-21 12:27:04.927 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:456 | 工作表 A岗职工 使用智能默认配置
2025-07-21 12:27:04.927 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:744 | 为Sheet 'A岗职工' 生成智能默认配置: 2 个必需字段
2025-07-21 12:27:04.927 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:139 | 为模板 a_grade_employees 生成了 26 个字段映射
2025-07-21 12:27:04.927 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:473 | 使用专用模板 a_grade_employees 生成字段映射: 26 个字段
2025-07-21 12:27:04.927 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:615 | 完整字段映射保存成功: salary_data_2025_07_a_grade_employees
2025-07-21 12:27:04.927 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:520 | 为表 salary_data_2025_07_a_grade_employees 生成标准化字段映射: 26 个字段
2025-07-21 12:27:04.942 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:541 | Sheet A岗职工 数据处理完成: 62 行
2025-07-21 12:27:04.942 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:371 | Sheet 'A岗职工' 检测到模板类型: a_grade_employees
2025-07-21 12:27:04.961 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:700 | 成功创建表: salary_data_2025_07_a_grade_employees
2025-07-21 12:27:04.961 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:360 | 专用工资数据表创建成功: salary_data_2025_07_a_grade_employees (模板: a_grade_employees)
2025-07-21 12:27:04.973 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1135 | 🔧 [修复标识] 导入字段映射加载完成: 26 个映射规则
2025-07-21 12:27:04.973 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1143 | 🔧 [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-07-21 12:27:04.973 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1152 | 🔧 [修复标识] 导入列名映射成功: 23 个字段已映射
2025-07-21 12:27:04.989 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1289 | 成功向表 salary_data_2025_07_a_grade_employees 保存 62 条数据。
2025-07-21 12:27:04.989 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:241 | 多Sheet导入完成: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2025_07_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2025_07_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2025_07_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2025_07_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2025_07_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']}
2025-07-21 12:27:04.989 | INFO     | src.gui.main_dialogs:_execute_multi_sheet_import:1479 | 多Sheet导入成功: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2025_07_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2025_07_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2025_07_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2025_07_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2025_07_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2025-07', 'data_description': '2025年7月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2025年 > 07月 > 全部在职人员'}
2025-07-21 12:27:04.989 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:4347 | 开始处理导入结果: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2025_07_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2025_07_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2025_07_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2025_07_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2025_07_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2025-07', 'data_description': '2025年7月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2025年 > 07月 > 全部在职人员'}
2025-07-21 12:27:05.004 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:4358 | 导入模式: multi_sheet, 目标路径: '工资表 > 2025年 > 07月 > 全部在职人员'
2025-07-21 12:27:05.004 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:4376 | 接收到导入数据, 来源: 未知来源, 目标路径: 工资表 > 2025年 > 07月 > 全部在职人员
2025-07-21 12:27:05.805 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:4464 | 检查是否需要更新导航面板: ['工资表', '2025年', '07月', '全部在职人员']
2025-07-21 12:27:05.805 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:4468 | 检测到工资数据导入，开始刷新导航面板
2025-07-21 12:27:05.808 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:4472 | 使用强制刷新方法
2025-07-21 12:27:05.809 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1230 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-07-21 12:27:05.810 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1238 | 找到工资表节点: 📊 工资表
2025-07-21 12:27:05.821 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:885 | 找到 4 个匹配类型 'salary_data' 的表
2025-07-21 12:27:05.822 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2025年，包含 1 个月份
2025-07-21 12:27:05.823 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1343 | 工资数据导航已强制刷新: 1 个年份, 1 个月份
2025-07-21 12:27:05.823 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1346 | 默认路径: 工资表 > 2025年 > 7月 > 全部在职人员
2025-07-21 12:27:05.824 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1385 | force_refresh_salary_data 执行完成
2025-07-21 12:27:05.825 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:4477 | 将在1500ms后导航到: 工资表 > 2025年 > 07月 > 全部在职人员
2025-07-21 12:27:07.328 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:4553 | 尝试导航到新导入的路径: 工资表 > 2025年 > 07月 > 全部在职人员
2025-07-21 12:27:07.329 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:4558 | 已成功导航到新导入的路径: 工资表 > 2025年 > 07月 > 全部在职人员
2025-07-21 12:27:07.835 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:5824 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '07月', '全部在职人员'] -> salary_data_2025_07_active_employees
2025-07-21 12:27:07.835 | INFO     | src.gui.prototype.prototype_main_window:_refresh_current_data_display:4646 | 开始刷新当前数据显示: salary_data_2025_07_active_employees
2025-07-21 12:27:07.839 | INFO     | src.gui.prototype.prototype_main_window:_refresh_current_data_display:4653 | 分页模式刷新: 第1页，每页50条
2025-07-21 12:27:07.839 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:5387 | 使用分页模式加载 salary_data_2025_07_active_employees，第1页，每页50条
2025-07-21 12:27:07.841 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:5476 | 缓存未命中，从数据库加载: salary_data_2025_07_active_employees 第1页
2025-07-21 12:27:07.845 | INFO     | src.gui.prototype.prototype_main_window:run:129 | 开始加载表 salary_data_2025_07_active_employees 第1页数据，每页50条
2025-07-21 12:27:07.846 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_07_active_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=0列
2025-07-21 12:27:07.852 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | 🔧 [P0-CRITICAL修复] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_07_active_employees" LIMIT 50 OFFSET 0
2025-07-21 12:27:07.858 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:557 | 🔧 [P0-CRITICAL修复] 数据库查询返回的前5个工号: ['19990089.0', '20161565.0', '20191782.0', '20151515.0', '20181640.0']
2025-07-21 12:27:07.861 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:569 | 成功从表 salary_data_2025_07_active_employees 获取第1页数据（含排序）: 50 行，总计1396行
2025-07-21 12:27:07.867 | INFO     | src.gui.prototype.prototype_main_window:run:155 | 使用排序查询: 0 个排序列
2025-07-21 12:27:07.868 | INFO     | src.gui.prototype.prototype_main_window:run:186 | 原始数据: 50行, 28列
2025-07-21 12:27:07.869 | INFO     | src.gui.prototype.prototype_main_window:run:193 | 开始应用字段映射
2025-07-21 12:27:07.870 | INFO     | src.gui.prototype.prototype_main_window:_apply_unified_field_processing:4975 | 开始统一字段处理: salary_data_2025_07_active_employees, 原始列数: 28
2025-07-21 12:27:07.876 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5172 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 12:27:07.883 | INFO     | src.gui.prototype.prototype_main_window:_apply_unified_field_processing:5024 | 🔧 [字段处理] 统一字段处理完成并缓存: 24个字段
2025-07-21 12:27:07.884 | INFO     | src.gui.prototype.prototype_main_window:run:203 | PaginationWorker - 字段映射成功: 28 -> 24列
2025-07-21 12:27:07.885 | INFO     | src.gui.prototype.prototype_main_window:run:217 | 字段映射成功: 24列
2025-07-21 12:27:07.887 | INFO     | src.gui.prototype.prototype_main_window:run:243 | 最终数据: 50行, 24列, 总记录数: 1396
2025-07-21 12:27:07.889 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:5507 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-07-21 12:27:07.891 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:5541 | 🚀 [性能缓存] 数据已缓存: salary_data_2025_07_active_employees 第1页
2025-07-21 12:27:07.904 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:439 | 正在从表 salary_data_2025_07_active_employees 分页获取数据: 第2页, 每页50条
2025-07-21 12:27:07.904 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:5570 | 🧹 [异步分页] 使用重构后的统一格式化结果: 50行, 24列
2025-07-21 12:27:07.908 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:462 | 成功从表 salary_data_2025_07_active_employees 获取第2页数据: 50 行，总计1396行
2025-07-21 12:27:07.909 | INFO     | src.gui.prototype.prototype_main_window:set_data:685 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-21 12:27:07.916 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5172 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 12:27:07.924 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 1000 -> 50
2025-07-21 12:27:07.924 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 12:27:07.987 | WARNING  | src.modules.format_management.master_format_manager:_load_format_config:319 | 配置文件不存在: state/data/format_config.json
2025-07-21 12:27:07.987 | INFO     | src.modules.format_management.master_format_manager:__init__:148 | 🎯 [统一格式化] 主格式化管理器已初始化
2025-07-21 12:27:08.017 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:238 | 开始格式化工资表数据 - 表类型: active_employees
2025-07-21 12:27:08.033 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:251 | 原始数据: 50行, 24列
2025-07-21 12:27:08.033 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:255 | 🔧 [表名映射] 输入表名: active_employees, 提取的表类型: active_employees
2025-07-21 12:27:08.033 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:259 | 使用表类型配置: active_employees, 配置字段: ['id_fields', 'string_fields', 'integer_fields', 'float_fields', 'special_fields']
2025-07-21 12:27:08.033 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:263 | 🔧 [格式化调试] 开始处理ID字段: ['employee_id', 'employee_type_code', '工号', '人员类别代码']
2025-07-21 12:27:08.033 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 工号 字段样例: ['19990089.0', '20161565.0', '20191782.0']
2025-07-21 12:27:08.033 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 人员类别代码 字段样例: ['1.0', '1.0', '17.0']
2025-07-21 12:27:08.033 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 工号: 原始样例=['19990089.0', '20161565.0', '20191782.0'], 原始类型=object
2025-07-21 12:27:08.033 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 工号: 样例=['19990089', '20161565', '20191782']
2025-07-21 12:27:08.033 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 工号: 样例=['19990089', '20161565', '20191782']
2025-07-21 12:27:08.033 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 工号 格式化完成，无小数点问题
2025-07-21 12:27:08.033 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 人员类别代码: 原始样例=['1.0', '1.0', '17.0'], 原始类型=object
2025-07-21 12:27:08.033 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 人员类别代码: 样例=['1', '1', '17']
2025-07-21 12:27:08.033 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 人员类别代码: 样例=['01', '01', '17']
2025-07-21 12:27:08.033 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 人员类别代码 格式化完成，无小数点问题
2025-07-21 12:27:08.033 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 工号 字段样例: ['19990089', '20161565', '20191782']
2025-07-21 12:27:08.033 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 人员类别代码 字段样例: ['01', '01', '17']
2025-07-21 12:27:08.049 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:472 | 🔧 [强化调试] 开始格式化字符串字段: ['employee_id', 'employee_name', 'department', 'employee_type_code', 'employee_type', '工号', '姓名', '部门名称', '人员类别代码', '人员类别']
2025-07-21 12:27:08.049 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 工号: 原始样例=['19990089', '20161565', '20191782'], 原始类型=object
2025-07-21 12:27:08.064 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:504 | 🔧 [修复] 工号字段 工号 已特殊处理，去除小数点格式
2025-07-21 12:27:08.064 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 工号: 样例=['19990089', '20161565', '20191782'], 类型=object
2025-07-21 12:27:08.064 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 工号 格式化完成，无小数点问题
2025-07-21 12:27:08.064 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 姓名: 原始样例=['杨胜', '胡四平', '肖啸'], 原始类型=object
2025-07-21 12:27:08.064 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 姓名: 样例=['杨胜', '胡四平', '肖啸'], 类型=object
2025-07-21 12:27:08.064 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 姓名 格式化完成，无小数点问题
2025-07-21 12:27:08.064 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 部门名称: 原始样例=['自动化学院', '自动化学院', '自动化学院'], 原始类型=object
2025-07-21 12:27:08.064 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 部门名称: 样例=['自动化学院', '自动化学院', '自动化学院'], 类型=object
2025-07-21 12:27:08.064 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 部门名称 格式化完成，无小数点问题
2025-07-21 12:27:08.064 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 人员类别代码: 原始样例=['01', '01', '17'], 原始类型=object
2025-07-21 12:27:08.064 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 人员类别代码: 样例=['01', '01', '17'], 类型=object
2025-07-21 12:27:08.064 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 人员类别代码 格式化完成，无小数点问题
2025-07-21 12:27:08.064 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 人员类别: 原始样例=['教学院其它人员', '教学单位专技人员', '科研单位人员'], 原始类型=object
2025-07-21 12:27:08.080 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 人员类别: 样例=['教学院其它人员', '教学单位专技人员', '科研单位人员'], 类型=object
2025-07-21 12:27:08.096 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 人员类别 格式化完成，无小数点问题
2025-07-21 12:27:08.096 | INFO     | src.modules.data_storage.salary_data_formatter:_format_integer_fields:604 | 🔧 [强化调试] 开始格式化整型字段: []
2025-07-21 12:27:08.096 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:655 | 🔧 [强化调试] 开始格式化浮点型字段: ['position_salary_2025', 'grade_salary_2025', 'allowance', 'balance_allowance', 'basic_performance_2025', 'health_fee', 'transport_allowance', 'property_allowance', 'communication_allowance', 'performance_bonus_2025', 'provident_fund_2025', 'housing_allowance', 'car_allowance', 'supplement', 'advance', 'total_salary', 'pension_insurance', '2025年岗位工资', '2025年薪级工资', '津贴', '结余津贴', '2025年基础性绩效', '卫生费', '交通补贴', '物业补贴', '通讯补贴', '2025年奖励性绩效预发', '2025公积金', '住房补贴', '车补', '补发', '借支', '应发工资', '代扣代存养老保险']
2025-07-21 12:27:08.096 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年岗位工资: 原始样例=[2880.0, 3030.0, 2185.0], 原始类型=float64
2025-07-21 12:27:08.096 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年岗位工资: 样例=[2880.0, 3030.0, 2185.0], 类型=float64
2025-07-21 12:27:08.096 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年岗位工资 格式化完成，保留两位小数
2025-07-21 12:27:08.096 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年薪级工资: 原始样例=[2375.0, 1696.0, 1427.0], 原始类型=float64
2025-07-21 12:27:08.096 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年薪级工资: 样例=[2375.0, 1696.0, 1427.0], 类型=float64
2025-07-21 12:27:08.096 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年薪级工资 格式化完成，保留两位小数
2025-07-21 12:27:08.096 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 津贴: 原始样例=[102.0, 0.0, 0.0], 原始类型=float64
2025-07-21 12:27:08.096 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 津贴: 样例=[102.0, 0.0, 0.0], 类型=float64
2025-07-21 12:27:08.096 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 津贴 格式化完成，保留两位小数
2025-07-21 12:27:08.096 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 结余津贴: 原始样例=[56.0, 56.0, 56.0], 原始类型=float64
2025-07-21 12:27:08.096 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 结余津贴: 样例=[56.0, 56.0, 56.0], 类型=float64
2025-07-21 12:27:08.096 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 结余津贴 格式化完成，保留两位小数
2025-07-21 12:27:08.111 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年基础性绩效: 原始样例=[3594.0, 3466.0, 2978.0], 原始类型=float64
2025-07-21 12:27:08.111 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年基础性绩效: 样例=[3594.0, 3466.0, 2978.0], 类型=float64
2025-07-21 12:27:08.111 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年基础性绩效 格式化完成，保留两位小数
2025-07-21 12:27:08.111 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 卫生费: 原始样例=[0.0, 0.0, 0.0], 原始类型=float64
2025-07-21 12:27:08.127 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 卫生费: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-21 12:27:08.127 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 卫生费 格式化完成，保留两位小数
2025-07-21 12:27:08.127 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 交通补贴: 原始样例=[220.0, 220.0, 220.0], 原始类型=float64
2025-07-21 12:27:08.127 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 交通补贴: 样例=[220.0, 220.0, 220.0], 类型=float64
2025-07-21 12:27:08.127 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 交通补贴 格式化完成，保留两位小数
2025-07-21 12:27:08.127 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 物业补贴: 原始样例=[240.0, 240.0, 200.0], 原始类型=float64
2025-07-21 12:27:08.127 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 物业补贴: 样例=[240.0, 240.0, 200.0], 类型=float64
2025-07-21 12:27:08.127 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 物业补贴 格式化完成，保留两位小数
2025-07-21 12:27:08.127 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 通讯补贴: 原始样例=[50.0, nan, nan], 原始类型=float64
2025-07-21 12:27:08.127 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 通讯补贴: 样例=[50.0, 0.0, 0.0], 类型=float64
2025-07-21 12:27:08.127 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 通讯补贴 格式化完成，保留两位小数
2025-07-21 12:27:08.127 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年奖励性绩效预发: 原始样例=[2500.0, 1000.0, 1000.0], 原始类型=float64
2025-07-21 12:27:08.127 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年奖励性绩效预发: 样例=[2500.0, 1000.0, 1000.0], 类型=float64
2025-07-21 12:27:08.127 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年奖励性绩效预发 格式化完成，保留两位小数
2025-07-21 12:27:08.127 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025公积金: 原始样例=[2097.0, 1860.0, 1984.0], 原始类型=float64
2025-07-21 12:27:08.127 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025公积金: 样例=[2097.0, 1860.0, 1984.0], 类型=float64
2025-07-21 12:27:08.127 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025公积金 格式化完成，保留两位小数
2025-07-21 12:27:08.143 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 住房补贴: 原始样例=[271.9745083294993, 189.0, 174.16354166666667], 原始类型=float64
2025-07-21 12:27:08.158 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 住房补贴: 样例=[271.97, 189.0, 174.16], 类型=float64
2025-07-21 12:27:08.158 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 住房补贴 格式化完成，保留两位小数
2025-07-21 12:27:08.158 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 车补: 原始样例=[None, None, None], 原始类型=object
2025-07-21 12:27:08.158 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 车补: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-21 12:27:08.158 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 车补 格式化完成，保留两位小数
2025-07-21 12:27:08.158 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 补发: 原始样例=[None, None, None], 原始类型=object
2025-07-21 12:27:08.158 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 补发: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-21 12:27:08.158 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 补发 格式化完成，保留两位小数
2025-07-21 12:27:08.158 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 借支: 原始样例=[nan, nan, 2000.0], 原始类型=float64
2025-07-21 12:27:08.158 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 借支: 样例=[0.0, 0.0, 2000.0], 类型=float64
2025-07-21 12:27:08.158 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 借支 格式化完成，保留两位小数
2025-07-21 12:27:08.158 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 应发工资: 原始样例=[12288.9745083295, 9897.0, 6240.163541666667], 原始类型=float64
2025-07-21 12:27:08.174 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 应发工资: 样例=[12288.97, 9897.0, 6240.16], 类型=float64
2025-07-21 12:27:08.174 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 应发工资 格式化完成，保留两位小数
2025-07-21 12:27:08.174 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 代扣代存养老保险: 原始样例=[1525.8000000000002, 1140.53, 1113.75], 原始类型=float64
2025-07-21 12:27:08.174 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 代扣代存养老保险: 样例=[1525.8, 1140.53, 1113.75], 类型=float64
2025-07-21 12:27:08.189 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 代扣代存养老保险 格式化完成，保留两位小数
2025-07-21 12:27:08.189 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:711 | 🔧 [特殊字段] 开始处理表类型 active_employees 的月份、年份字段
2025-07-21 12:27:08.189 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:866 | 🔧 [特殊字段] 月份、年份字段处理完成
2025-07-21 12:27:08.189 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:291 | 数据格式化完成: 50行, 24列
2025-07-21 12:27:08.189 | INFO     | src.modules.format_management.master_format_manager:format_table_data:187 | 🎯 [统一格式化] 数据格式化完成: active_employees
2025-07-21 12:27:08.189 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 12:27:08.189 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 12:27:08.189 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 12:27:08.189 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 12:27:08.189 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 12:27:08.189 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 12:27:08.189 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 12:27:08.189 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 12:27:08.205 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 12:27:08.205 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 12:27:08.205 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 12:27:08.205 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 12:27:08.205 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 12:27:08.221 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 12:27:08.221 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 12:27:08.236 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:410 | 🔧 [新架构] 排序管理器切换到表格: salary_data_2025_07_active_employees
2025-07-21 12:27:08.236 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:442 | 🔧 [排序修复] 为表格 salary_data_2025_07_active_employees 重新加载 28 个字段映射
2025-07-21 12:27:08.236 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 312.0ms
2025-07-21 12:27:08.236 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 12:27:08.236 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6315 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-07-21 12:27:08.252 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1590 | 🆕 [列宽保存] 未找到表格 salary_data_2025_07_active_employees 的列宽配置
2025-07-21 12:27:08.252 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 50
2025-07-21 12:27:08.252 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 12:27:19.792 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1564 | 列宽设置已保存: salary_data_2025_07_active_employees (24 列)
2025-07-21 12:27:22.472 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1564 | 列宽设置已保存: salary_data_2025_07_active_employees (24 列)
2025-07-21 12:27:23.221 | INFO     | src.gui.prototype.widgets.column_sort_manager:handle_header_click:132 | 🆕 [多列排序] 新增列6(2025年薪级工资)排序: 升序
2025-07-21 12:27:23.221 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_state_changed:5796 | 🆕 [新架构多列排序] 排序状态变化: 1 列
2025-07-21 12:27:23.224 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_state_changed:5804 | 🆕 [新架构多列排序] 当前排序: 2025年薪级工资: 升序
2025-07-21 12:27:23.225 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_request:5819 | 🆕 [新架构多列排序] 排序请求: salary_data_2025_07_active_employees.grade_salary_2025 -> ascending
2025-07-21 12:27:23.228 | INFO     | src.gui.prototype.prototype_main_window:_handle_sort_applied:3547 | 🆕 [新架构排序] 处理排序应用: 列6, grade_salary_2025, ascending
2025-07-21 12:27:23.228 | INFO     | src.gui.prototype.prototype_main_window:_handle_sort_applied:3592 | 当前页码: 1
2025-07-21 12:27:23.229 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:3340 | 🔧 [排序] 使用实际页码: 1 (传入页码: 1)
2025-07-21 12:27:23.230 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:3361 | 🔧 [排序] 字段转换: grade_salary_2025 -> grade_salary_2025
2025-07-21 12:27:23.231 | INFO     | src.services.table_data_service:_handle_sort_request:140 | 处理排序请求: salary_data_2025_07_active_employees
2025-07-21 12:27:23.231 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:3380 | 🔧 [排序] 已发布排序请求事件: salary_data_2025_07_active_employees, 1列
2025-07-21 12:27:23.232 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_header_clicked:6489 | 🆕 [新架构多列排序] 成功处理列6(2025年薪级工资)点击
2025-07-21 12:27:23.233 | INFO     | src.core.unified_data_request_manager:request_table_data:200 | 开始处理数据请求: salary_data_2025_07_active_employees, 类型: sort_change
2025-07-21 12:27:23.240 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 12:27:23.241 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 12:27:23.242 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_07_active_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=1列
2025-07-21 12:27:23.242 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:516 | 🔧 [排序修复] 准备排序列: grade_salary_2025
2025-07-21 12:27:23.244 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 12:27:23.245 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:523 | 🔧 [排序修复] 列 'grade_salary_2025' 验证通过，开始排序
2025-07-21 12:27:23.245 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:532 | 🔧 [排序修复] 数值列排序: grade_salary_2025 ASC
2025-07-21 12:27:23.246 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:539 | 🔧 [排序修复] 添加排序子句: CAST("grade_salary_2025" AS REAL) ASC
2025-07-21 12:27:23.252 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | 🔧 [P0-CRITICAL修复] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_07_active_employees" ORDER BY CAST("grade_salary_2025" AS REAL) ASC LIMIT 50 OFFSET 0
2025-07-21 12:27:23.258 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:557 | 🔧 [P0-CRITICAL修复] 数据库查询返回的前5个工号: ['19961347.0', '20251003.0', '20251006.0', '20251007.0', '20251008.0']
2025-07-21 12:27:23.260 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:566 | 🔧 [P0-CRITICAL修复] 查询结果中 grade_salary_2025 的前10个值: [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 596.0]
2025-07-21 12:27:23.267 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:569 | 成功从表 salary_data_2025_07_active_employees 获取第1页数据（含排序）: 50 行，总计1396行
2025-07-21 12:27:23.268 | INFO     | src.core.unified_data_request_manager:request_table_data:249 | 数据请求处理完成: 28字段, 50行, 耗时35.1ms
2025-07-21 12:27:23.269 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:2973 | 🆕 接收到新架构数据更新事件: salary_data_2025_07_active_employees
2025-07-21 12:27:23.269 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:2998 | 数据内容: 50行 x 28列
2025-07-21 12:27:23.270 | INFO     | src.gui.prototype.prototype_main_window:set_data:685 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-21 12:27:23.272 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:1181 | 过滤了 4 个系统字段
2025-07-21 12:27:23.275 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5172 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 12:27:23.283 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 12:27:23.283 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 12:27:23.288 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 12:27:23.291 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 12:27:23.292 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 12:27:23.293 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 12:27:23.294 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 12:27:23.295 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 12:27:23.296 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 12:27:23.297 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 12:27:23.347 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 12:27:23.348 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 12:27:23.349 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 12:27:23.350 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 12:27:23.350 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 12:27:23.352 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 12:27:23.355 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 12:27:23.448 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 166.7ms
2025-07-21 12:27:23.449 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 12:27:23.450 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 12:27:23.451 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 升序
2025-07-21 12:27:23.465 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 12:27:23.467 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 50
2025-07-21 12:27:23.467 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 12:27:23.471 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3028 | 🔧 [分页修复] 数据更新事件设置总记录数: 1396
2025-07-21 12:27:23.472 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3039 | 🔧 [分页修复] 分页状态: 当前页=1, 总页数=28, 总记录数=1396
2025-07-21 12:27:23.473 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3045 | 🔧 [分页修复] 按钮可用性: 下一页=True, 上一页=False
2025-07-21 12:27:24.000 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1564 | 列宽设置已保存: salary_data_2025_07_active_employees (24 列)
2025-07-21 12:27:27.886 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3823 | 🔧 [紧急修复] 新架构分页: 第2页
2025-07-21 12:27:27.886 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3835 | 新架构分页时应用排序: 1列
2025-07-21 12:27:27.886 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 2
2025-07-21 12:27:27.886 | INFO     | src.core.unified_data_request_manager:request_table_data:200 | 开始处理数据请求: salary_data_2025_07_active_employees, 类型: initial_load
2025-07-21 12:27:27.886 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 12:27:27.886 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 12:27:27.886 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_07_active_employees 分页获取数据（支持排序）: 第2页, 每页50条, 排序=1列
2025-07-21 12:27:27.886 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:516 | 🔧 [排序修复] 准备排序列: grade_salary_2025
2025-07-21 12:27:27.886 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 12:27:27.886 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:523 | 🔧 [排序修复] 列 'grade_salary_2025' 验证通过，开始排序
2025-07-21 12:27:27.886 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:532 | 🔧 [排序修复] 数值列排序: grade_salary_2025 ASC
2025-07-21 12:27:27.902 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:539 | 🔧 [排序修复] 添加排序子句: CAST("grade_salary_2025" AS REAL) ASC
2025-07-21 12:27:27.902 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | 🔧 [P0-CRITICAL修复] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_07_active_employees" ORDER BY CAST("grade_salary_2025" AS REAL) ASC LIMIT 50 OFFSET 50
2025-07-21 12:27:27.902 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:557 | 🔧 [P0-CRITICAL修复] 数据库查询返回的前5个工号: ['20211012.0', '20221065.0', '20231054.0', '20241001.0', '20231033.0']
2025-07-21 12:27:27.902 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:566 | 🔧 [P0-CRITICAL修复] 查询结果中 grade_salary_2025 的前10个值: [1005.0, 1005.0, 1005.0, 1005.0, 1005.0, 1005.0, 1087.0, 1087.0, 1087.0, 1087.0]
2025-07-21 12:27:27.918 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:569 | 成功从表 salary_data_2025_07_active_employees 获取第2页数据（含排序）: 50 行，总计1396行
2025-07-21 12:27:27.918 | INFO     | src.core.unified_data_request_manager:request_table_data:249 | 数据请求处理完成: 28字段, 50行, 耗时31.4ms
2025-07-21 12:27:27.918 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:238 | 开始格式化工资表数据 - 表类型: active_employees
2025-07-21 12:27:27.918 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:251 | 原始数据: 50行, 28列
2025-07-21 12:27:27.918 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:255 | 🔧 [表名映射] 输入表名: active_employees, 提取的表类型: active_employees
2025-07-21 12:27:27.918 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:259 | 使用表类型配置: active_employees, 配置字段: ['id_fields', 'string_fields', 'integer_fields', 'float_fields', 'special_fields']
2025-07-21 12:27:27.918 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:263 | 🔧 [格式化调试] 开始处理ID字段: ['employee_id', 'employee_type_code', '工号', '人员类别代码']
2025-07-21 12:27:27.918 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 工号 字段样例: ['20211012.0', '20221065.0', '20231054.0']
2025-07-21 12:27:27.918 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:268 | 🔧 [格式化调试] 处理前 人员类别代码 字段样例: ['17.0', '1.0', '1.0']
2025-07-21 12:27:27.918 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 工号: 原始样例=['20211012.0', '20221065.0', '20231054.0'], 原始类型=object
2025-07-21 12:27:27.918 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 工号: 样例=['20211012', '20221065', '20231054']
2025-07-21 12:27:27.933 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 工号: 样例=['20211012', '20221065', '20231054']
2025-07-21 12:27:27.933 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 工号 格式化完成，无小数点问题
2025-07-21 12:27:27.933 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:377 | 🔧 [强化调试] 处理ID字段 人员类别代码: 原始样例=['17.0', '1.0', '1.0'], 原始类型=object
2025-07-21 12:27:27.933 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:426 | 🔧 [强化调试] 清理后ID字段 人员类别代码: 样例=['17', '1', '1']
2025-07-21 12:27:27.933 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:443 | 🔧 [强化调试] 最终ID字段 人员类别代码: 样例=['17', '01', '01']
2025-07-21 12:27:27.933 | INFO     | src.modules.data_storage.salary_data_formatter:_format_id_fields:454 | 🔧 [验证成功] ID字段 人员类别代码 格式化完成，无小数点问题
2025-07-21 12:27:27.933 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 工号 字段样例: ['20211012', '20221065', '20231054']
2025-07-21 12:27:27.933 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:274 | 🔧 [格式化调试] 处理后 人员类别代码 字段样例: ['17', '01', '01']
2025-07-21 12:27:27.933 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:472 | 🔧 [强化调试] 开始格式化字符串字段: ['employee_id', 'employee_name', 'department', 'employee_type_code', 'employee_type', '工号', '姓名', '部门名称', '人员类别代码', '人员类别']
2025-07-21 12:27:27.933 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 工号: 原始样例=['20211012', '20221065', '20231054'], 原始类型=object
2025-07-21 12:27:27.949 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:504 | 🔧 [修复] 工号字段 工号 已特殊处理，去除小数点格式
2025-07-21 12:27:27.949 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 工号: 样例=['20211012', '20221065', '20231054'], 类型=object
2025-07-21 12:27:27.949 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 工号 格式化完成，无小数点问题
2025-07-21 12:27:27.949 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 姓名: 原始样例=['孙珏', '夏成启', '渠梦男'], 原始类型=object
2025-07-21 12:27:27.949 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 姓名: 样例=['孙珏', '夏成启', '渠梦男'], 类型=object
2025-07-21 12:27:27.949 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 姓名 格式化完成，无小数点问题
2025-07-21 12:27:27.949 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 部门名称: 原始样例=['经济与管理学院', '教务处', '基础医学院'], 原始类型=object
2025-07-21 12:27:27.949 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 部门名称: 样例=['经济与管理学院', '教务处', '基础医学院'], 类型=object
2025-07-21 12:27:27.949 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 部门名称 格式化完成，无小数点问题
2025-07-21 12:27:27.965 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 人员类别代码: 原始样例=['17', '01', '01'], 原始类型=object
2025-07-21 12:27:27.965 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 人员类别代码: 样例=['17', '01', '01'], 类型=object
2025-07-21 12:27:27.965 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 人员类别代码 格式化完成，无小数点问题
2025-07-21 12:27:27.965 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:480 | 🔧 [强化调试] 处理字符串字段 人员类别: 原始样例=['管理单位人员', '管理单位人员', '教学单位专技人员'], 原始类型=object
2025-07-21 12:27:27.965 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:574 | 🔧 [强化调试] 格式化后字符串字段 人员类别: 样例=['管理单位人员', '管理单位人员', '教学单位专技人员'], 类型=object
2025-07-21 12:27:27.965 | INFO     | src.modules.data_storage.salary_data_formatter:_format_string_fields:585 | 🔧 [验证成功] 字符串字段 人员类别 格式化完成，无小数点问题
2025-07-21 12:27:27.965 | INFO     | src.modules.data_storage.salary_data_formatter:_format_integer_fields:604 | 🔧 [强化调试] 开始格式化整型字段: []
2025-07-21 12:27:27.965 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:655 | 🔧 [强化调试] 开始格式化浮点型字段: ['position_salary_2025', 'grade_salary_2025', 'allowance', 'balance_allowance', 'basic_performance_2025', 'health_fee', 'transport_allowance', 'property_allowance', 'communication_allowance', 'performance_bonus_2025', 'provident_fund_2025', 'housing_allowance', 'car_allowance', 'supplement', 'advance', 'total_salary', 'pension_insurance', '2025年岗位工资', '2025年薪级工资', '津贴', '结余津贴', '2025年基础性绩效', '卫生费', '交通补贴', '物业补贴', '通讯补贴', '2025年奖励性绩效预发', '2025公积金', '住房补贴', '车补', '补发', '借支', '应发工资', '代扣代存养老保险']
2025-07-21 12:27:27.980 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年岗位工资: 原始样例=[2200.0, 1925.0, 2185.0], 原始类型=float64
2025-07-21 12:27:27.980 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年岗位工资: 样例=[2200.0, 1925.0, 2185.0], 类型=float64
2025-07-21 12:27:27.980 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年岗位工资 格式化完成，保留两位小数
2025-07-21 12:27:27.980 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年薪级工资: 原始样例=[1005.0, 1005.0, 1005.0], 原始类型=float64
2025-07-21 12:27:27.980 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年薪级工资: 样例=[1005.0, 1005.0, 1005.0], 类型=float64
2025-07-21 12:27:27.980 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年薪级工资 格式化完成，保留两位小数
2025-07-21 12:27:27.980 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 津贴: 原始样例=[0.0, 0.0, 0.0], 原始类型=float64
2025-07-21 12:27:27.980 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 津贴: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-21 12:27:27.980 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 津贴 格式化完成，保留两位小数
2025-07-21 12:27:27.980 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 结余津贴: 原始样例=[56.0, 56.0, 56.0], 原始类型=float64
2025-07-21 12:27:27.980 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 结余津贴: 样例=[56.0, 56.0, 56.0], 类型=float64
2025-07-21 12:27:27.980 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 结余津贴 格式化完成，保留两位小数
2025-07-21 12:27:27.996 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年基础性绩效: 原始样例=[2824.0, 2696.0, 4311.0], 原始类型=float64
2025-07-21 12:27:27.996 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年基础性绩效: 样例=[2824.0, 2696.0, 4311.0], 类型=float64
2025-07-21 12:27:27.996 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年基础性绩效 格式化完成，保留两位小数
2025-07-21 12:27:27.996 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 卫生费: 原始样例=[20.0, 0.0, 20.0], 原始类型=float64
2025-07-21 12:27:27.996 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 卫生费: 样例=[20.0, 0.0, 20.0], 类型=float64
2025-07-21 12:27:27.996 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 卫生费 格式化完成，保留两位小数
2025-07-21 12:27:27.996 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 交通补贴: 原始样例=[220.0, 220.0, 220.0], 原始类型=float64
2025-07-21 12:27:27.996 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 交通补贴: 样例=[220.0, 220.0, 220.0], 类型=float64
2025-07-21 12:27:27.996 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 交通补贴 格式化完成，保留两位小数
2025-07-21 12:27:27.996 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 物业补贴: 原始样例=[200.0, 200.0, 200.0], 原始类型=float64
2025-07-21 12:27:28.011 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 物业补贴: 样例=[200.0, 200.0, 200.0], 类型=float64
2025-07-21 12:27:28.011 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 物业补贴 格式化完成，保留两位小数
2025-07-21 12:27:28.011 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 通讯补贴: 原始样例=[None, None, None], 原始类型=object
2025-07-21 12:27:28.011 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 通讯补贴: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-21 12:27:28.011 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 通讯补贴 格式化完成，保留两位小数
2025-07-21 12:27:28.011 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025年奖励性绩效预发: 原始样例=[500.0, 1500.0, 1600.0], 原始类型=float64
2025-07-21 12:27:28.011 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025年奖励性绩效预发: 样例=[500.0, 1500.0, 1600.0], 类型=float64
2025-07-21 12:27:28.011 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025年奖励性绩效预发 格式化完成，保留两位小数
2025-07-21 12:27:28.011 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 2025公积金: 原始样例=[1306.0, 1080.0, 1268.0], 原始类型=float64
2025-07-21 12:27:28.011 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 2025公积金: 样例=[1306.0, 1080.0, 1268.0], 类型=float64
2025-07-21 12:27:28.011 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 2025公积金 格式化完成，保留两位小数
2025-07-21 12:27:28.011 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 住房补贴: 原始样例=[143.0, 143.0, 189.0], 原始类型=float64
2025-07-21 12:27:28.027 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 住房补贴: 样例=[143.0, 143.0, 189.0], 类型=float64
2025-07-21 12:27:28.027 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 住房补贴 格式化完成，保留两位小数
2025-07-21 12:27:28.027 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 车补: 原始样例=[None, None, None], 原始类型=object
2025-07-21 12:27:28.027 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 车补: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-21 12:27:28.027 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 车补 格式化完成，保留两位小数
2025-07-21 12:27:28.027 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 补发: 原始样例=[None, None, None], 原始类型=object
2025-07-21 12:27:28.027 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 补发: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-21 12:27:28.027 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 补发 格式化完成，保留两位小数
2025-07-21 12:27:28.027 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 借支: 原始样例=[nan, nan, nan], 原始类型=float64
2025-07-21 12:27:28.027 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 借支: 样例=[0.0, 0.0, 0.0], 类型=float64
2025-07-21 12:27:28.027 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 借支 格式化完成，保留两位小数
2025-07-21 12:27:28.043 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 应发工资: 原始样例=[7168.0, 7745.0, 9786.0], 原始类型=float64
2025-07-21 12:27:28.043 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 应发工资: 样例=[7168.0, 7745.0, 9786.0], 类型=float64
2025-07-21 12:27:28.043 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 应发工资 格式化完成，保留两位小数
2025-07-21 12:27:28.043 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:663 | 🔧 [强化调试] 处理浮点型字段 代扣代存养老保险: 原始样例=[978.47, 961.93, 971.8], 原始类型=float64
2025-07-21 12:27:28.043 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:675 | 🔧 [强化调试] 转换后浮点型字段 代扣代存养老保险: 样例=[978.47, 961.93, 971.8], 类型=float64
2025-07-21 12:27:28.043 | INFO     | src.modules.data_storage.salary_data_formatter:_format_float_fields:686 | 🔧 [验证成功] 浮点型字段 代扣代存养老保险 格式化完成，保留两位小数
2025-07-21 12:27:28.043 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:711 | 🔧 [特殊字段] 开始处理表类型 active_employees 的月份、年份字段
2025-07-21 12:27:28.043 | INFO     | src.modules.data_storage.salary_data_formatter:_format_special_fields:866 | 🔧 [特殊字段] 月份、年份字段处理完成
2025-07-21 12:27:28.058 | INFO     | src.modules.data_storage.salary_data_formatter:format_table_data:291 | 数据格式化完成: 50行, 28列
2025-07-21 12:27:28.058 | INFO     | src.modules.format_management.master_format_manager:format_table_data:187 | 🎯 [统一格式化] 数据格式化完成: active_employees
2025-07-21 12:27:28.058 | INFO     | src.services.table_data_service:load_table_data:347 | 🎯 [统一格式化] 数据格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 12:27:28.058 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:2973 | 🆕 接收到新架构数据更新事件: salary_data_2025_07_active_employees
2025-07-21 12:27:28.058 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:2998 | 数据内容: 50行 x 28列
2025-07-21 12:27:28.058 | INFO     | src.gui.prototype.prototype_main_window:set_data:685 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-21 12:27:28.058 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:1181 | 过滤了 4 个系统字段
2025-07-21 12:27:28.058 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5172 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 12:27:28.074 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 12:27:28.074 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 12:27:28.074 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 12:27:28.074 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 12:27:28.074 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 12:27:28.074 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 12:27:28.074 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 12:27:28.074 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 12:27:28.090 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 12:27:28.090 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 12:27:28.090 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 12:27:28.090 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 12:27:28.090 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 12:27:28.090 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 12:27:28.090 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 12:27:28.090 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 12:27:28.090 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 12:27:28.121 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 47.0ms
2025-07-21 12:27:28.121 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 12:27:28.121 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 12:27:28.121 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 升序
2025-07-21 12:27:28.121 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 12:27:28.121 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 50
2025-07-21 12:27:28.121 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3823 | 🔧 [紧急修复] 新架构分页: 第1页
2025-07-21 12:27:28.121 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3835 | 新架构分页时应用排序: 1列
2025-07-21 12:27:28.121 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 1
2025-07-21 12:27:28.121 | INFO     | src.core.unified_data_request_manager:request_table_data:200 | 开始处理数据请求: salary_data_2025_07_active_employees, 类型: initial_load
2025-07-21 12:27:28.121 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 12:27:28.121 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 12:27:28.121 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_07_active_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=1列
2025-07-21 12:27:28.137 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:516 | 🔧 [排序修复] 准备排序列: grade_salary_2025
2025-07-21 12:27:28.137 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 12:27:28.137 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:523 | 🔧 [排序修复] 列 'grade_salary_2025' 验证通过，开始排序
2025-07-21 12:27:28.137 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:532 | 🔧 [排序修复] 数值列排序: grade_salary_2025 ASC
2025-07-21 12:27:28.137 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:539 | 🔧 [排序修复] 添加排序子句: CAST("grade_salary_2025" AS REAL) ASC
2025-07-21 12:27:28.137 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | 🔧 [P0-CRITICAL修复] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_07_active_employees" ORDER BY CAST("grade_salary_2025" AS REAL) ASC LIMIT 50 OFFSET 0
2025-07-21 12:27:28.152 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:557 | 🔧 [P0-CRITICAL修复] 数据库查询返回的前5个工号: ['19961347.0', '20251003.0', '20251006.0', '20251007.0', '20251008.0']
2025-07-21 12:27:28.152 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:566 | 🔧 [P0-CRITICAL修复] 查询结果中 grade_salary_2025 的前10个值: [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 596.0]
2025-07-21 12:27:28.152 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:569 | 成功从表 salary_data_2025_07_active_employees 获取第1页数据（含排序）: 50 行，总计1396行
2025-07-21 12:27:28.152 | INFO     | src.core.unified_data_request_manager:request_table_data:249 | 数据请求处理完成: 28字段, 50行, 耗时30.9ms
2025-07-21 12:27:28.152 | INFO     | src.services.table_data_service:load_table_data:347 | 🎯 [统一格式化] 数据格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 12:27:28.168 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5172 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 12:27:28.168 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 12:27:28.168 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 12:27:28.183 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 12:27:28.183 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 12:27:28.183 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 12:27:28.183 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 12:27:28.183 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 12:27:28.183 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 12:27:28.183 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 12:27:28.183 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 12:27:28.183 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 12:27:28.183 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 12:27:28.183 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 12:27:28.183 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 12:27:28.183 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 12:27:28.183 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 12:27:28.200 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 12:27:28.215 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 47.1ms
2025-07-21 12:27:28.215 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 12:27:28.215 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 12:27:28.215 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 升序
2025-07-21 12:27:28.215 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 12:27:28.230 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 12:27:28.230 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3898 | 🔧 [紧急修复] 新架构分页成功: 第1页, 50行
2025-07-21 12:27:28.230 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3823 | 🔧 [紧急修复] 新架构分页: 第1页
2025-07-21 12:27:28.230 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3835 | 新架构分页时应用排序: 1列
2025-07-21 12:27:28.230 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 1
2025-07-21 12:27:28.246 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5172 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 12:27:28.246 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 12:27:28.246 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 12:27:28.262 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 12:27:28.262 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 12:27:28.262 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 12:27:28.262 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 12:27:28.262 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 12:27:28.262 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 12:27:28.262 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 12:27:28.262 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 12:27:28.262 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 12:27:28.262 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 12:27:28.262 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 12:27:28.262 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 12:27:28.262 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 12:27:28.277 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 12:27:28.277 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 12:27:28.293 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 46.5ms
2025-07-21 12:27:28.293 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 12:27:28.293 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 12:27:28.293 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 升序
2025-07-21 12:27:28.293 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 12:27:28.293 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 12:27:28.308 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3898 | 🔧 [紧急修复] 新架构分页成功: 第1页, 50行
2025-07-21 12:27:28.308 | INFO     | src.gui.widgets.pagination_widget:set_current_page:330 | 页码切换到: 1
2025-07-21 12:27:28.308 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3823 | 🔧 [紧急修复] 新架构分页: 第2页
2025-07-21 12:27:28.308 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3835 | 新架构分页时应用排序: 1列
2025-07-21 12:27:28.308 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 2
2025-07-21 12:27:28.324 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5172 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 12:27:28.324 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 12:27:28.324 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 12:27:28.340 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 12:27:28.340 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 12:27:28.340 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 12:27:28.340 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 12:27:28.340 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 12:27:28.340 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 12:27:28.340 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 12:27:28.340 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 12:27:28.340 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 12:27:28.340 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 12:27:28.340 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 12:27:28.340 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 12:27:28.340 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 12:27:28.355 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 12:27:28.355 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 12:27:28.371 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 47.0ms
2025-07-21 12:27:28.371 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 12:27:28.371 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 12:27:28.371 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 升序
2025-07-21 12:27:28.371 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 12:27:28.371 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 12:27:28.387 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3898 | 🔧 [紧急修复] 新架构分页成功: 第2页, 50行
2025-07-21 12:27:28.387 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3823 | 🔧 [紧急修复] 新架构分页: 第2页
2025-07-21 12:27:28.387 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3835 | 新架构分页时应用排序: 1列
2025-07-21 12:27:28.387 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 2
2025-07-21 12:27:28.387 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5172 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 12:27:28.402 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 12:27:28.402 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 12:27:28.402 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 12:27:28.402 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 12:27:28.402 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 12:27:28.421 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 12:27:28.421 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 12:27:28.421 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 12:27:28.421 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 12:27:28.421 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 12:27:28.421 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 12:27:28.421 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 12:27:28.421 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 12:27:28.421 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 12:27:28.421 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 12:27:28.434 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 12:27:28.434 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 12:27:28.449 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 47.1ms
2025-07-21 12:27:28.449 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 12:27:28.449 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 12:27:28.449 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 升序
2025-07-21 12:27:28.449 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 12:27:28.449 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 12:27:28.449 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3898 | 🔧 [紧急修复] 新架构分页成功: 第2页, 50行
2025-07-21 12:27:28.449 | INFO     | src.gui.widgets.pagination_widget:set_current_page:330 | 页码切换到: 2
2025-07-21 12:27:28.449 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3033 | 🔧 [分页修复] 数据更新事件设置当前页: 2
2025-07-21 12:27:28.449 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3039 | 🔧 [分页修复] 分页状态: 当前页=2, 总页数=28, 总记录数=1396
2025-07-21 12:27:28.449 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3045 | 🔧 [分页修复] 按钮可用性: 下一页=True, 上一页=True
2025-07-21 12:27:28.465 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5172 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 12:27:28.480 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 12:27:28.480 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 12:27:28.480 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 12:27:28.480 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 12:27:28.480 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 12:27:28.480 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 12:27:28.480 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 12:27:28.480 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 12:27:28.480 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 12:27:28.496 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 12:27:28.496 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 12:27:28.496 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 12:27:28.496 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 12:27:28.496 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 12:27:28.496 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 12:27:28.496 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 12:27:28.496 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 12:27:28.528 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 63.7ms
2025-07-21 12:27:28.528 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 12:27:28.528 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 12:27:28.528 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 升序
2025-07-21 12:27:28.528 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 12:27:28.528 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 12:27:28.528 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3898 | 🔧 [紧急修复] 新架构分页成功: 第2页, 50行
2025-07-21 12:27:28.528 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3823 | 🔧 [紧急修复] 新架构分页: 第2页
2025-07-21 12:27:28.528 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3835 | 新架构分页时应用排序: 1列
2025-07-21 12:27:28.528 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 2
2025-07-21 12:27:28.543 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5172 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 12:27:28.543 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 12:27:28.543 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 12:27:28.558 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 12:27:28.558 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 12:27:28.558 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 12:27:28.558 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 12:27:28.558 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 12:27:28.558 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 12:27:28.558 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 12:27:28.558 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 12:27:28.574 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 12:27:28.574 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 12:27:28.574 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 12:27:28.574 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 12:27:28.574 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 12:27:28.574 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 12:27:28.574 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 12:27:28.589 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 47.0ms
2025-07-21 12:27:28.605 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 12:27:28.605 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 12:27:28.605 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 升序
2025-07-21 12:27:28.605 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 12:27:28.605 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 12:27:28.605 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3898 | 🔧 [紧急修复] 新架构分页成功: 第2页, 50行
2025-07-21 12:27:28.605 | INFO     | src.gui.widgets.pagination_widget:set_current_page:330 | 页码切换到: 2
2025-07-21 12:27:29.105 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1564 | 列宽设置已保存: salary_data_2025_07_active_employees (24 列)
2025-07-21 12:27:33.215 | INFO     | src.gui.prototype.widgets.column_sort_manager:handle_header_click:128 | 🆕 [多列排序] 更新列6(2025年薪级工资)排序: descending
2025-07-21 12:27:33.215 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_state_changed:5796 | 🆕 [新架构多列排序] 排序状态变化: 1 列
2025-07-21 12:27:33.215 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_state_changed:5804 | 🆕 [新架构多列排序] 当前排序: 2025年薪级工资: 降序
2025-07-21 12:27:33.215 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_request:5819 | 🆕 [新架构多列排序] 排序请求: salary_data_2025_07_active_employees.grade_salary_2025 -> descending
2025-07-21 12:27:33.215 | INFO     | src.gui.prototype.prototype_main_window:_handle_sort_applied:3547 | 🆕 [新架构排序] 处理排序应用: 列6, grade_salary_2025, descending
2025-07-21 12:27:33.230 | INFO     | src.core.table_sort_state_manager:save_sort_state:229 | 已保存排序状态: salary_data_2025_07_active_employees (employees), 1 列
2025-07-21 12:27:33.230 | INFO     | src.gui.prototype.prototype_main_window:_handle_sort_applied:3586 | 表头排序状态已同步到管理器
2025-07-21 12:27:33.230 | INFO     | src.gui.prototype.prototype_main_window:_handle_sort_applied:3592 | 当前页码: 2
2025-07-21 12:27:33.230 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:3340 | 🔧 [排序] 使用实际页码: 2 (传入页码: 2)
2025-07-21 12:27:33.230 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:3361 | 🔧 [排序] 字段转换: grade_salary_2025 -> grade_salary_2025
2025-07-21 12:27:33.230 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:3380 | 🔧 [排序] 已发布排序请求事件: salary_data_2025_07_active_employees, 1列
2025-07-21 12:27:33.230 | INFO     | src.services.table_data_service:_handle_sort_request:140 | 处理排序请求: salary_data_2025_07_active_employees
2025-07-21 12:27:33.230 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_header_clicked:6489 | 🆕 [新架构多列排序] 成功处理列6(2025年薪级工资)点击
2025-07-21 12:27:33.230 | INFO     | src.core.unified_data_request_manager:request_table_data:200 | 开始处理数据请求: salary_data_2025_07_active_employees, 类型: sort_change
2025-07-21 12:27:33.230 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 12:27:33.230 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 12:27:33.230 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_07_active_employees 分页获取数据（支持排序）: 第2页, 每页50条, 排序=1列
2025-07-21 12:27:33.230 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:516 | 🔧 [排序修复] 准备排序列: grade_salary_2025
2025-07-21 12:27:33.246 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 12:27:33.246 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:523 | 🔧 [排序修复] 列 'grade_salary_2025' 验证通过，开始排序
2025-07-21 12:27:33.246 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:532 | 🔧 [排序修复] 数值列排序: grade_salary_2025 DESC
2025-07-21 12:27:33.246 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:539 | 🔧 [排序修复] 添加排序子句: CAST("grade_salary_2025" AS REAL) DESC
2025-07-21 12:27:33.246 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | 🔧 [P0-CRITICAL修复] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_07_active_employees" ORDER BY CAST("grade_salary_2025" AS REAL) DESC LIMIT 50 OFFSET 50
2025-07-21 12:27:33.277 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:557 | 🔧 [P0-CRITICAL修复] 数据库查询返回的前5个工号: ['19881135.0', '19911130.0', '19901013.0', '19891097.0', '19880780.0']
2025-07-21 12:27:33.277 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:566 | 🔧 [P0-CRITICAL修复] 查询结果中 grade_salary_2025 的前10个值: [3921.0, 3921.0, 3921.0, 3921.0, 3921.0, 3921.0, 3921.0, 3921.0, 3921.0, 3921.0]
2025-07-21 12:27:33.293 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:569 | 成功从表 salary_data_2025_07_active_employees 获取第2页数据（含排序）: 50 行，总计1396行
2025-07-21 12:27:33.293 | INFO     | src.core.unified_data_request_manager:request_table_data:249 | 数据请求处理完成: 28字段, 50行, 耗时62.3ms
2025-07-21 12:27:33.293 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:2973 | 🆕 接收到新架构数据更新事件: salary_data_2025_07_active_employees
2025-07-21 12:27:33.293 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:2998 | 数据内容: 50行 x 28列
2025-07-21 12:27:33.293 | INFO     | src.gui.prototype.prototype_main_window:set_data:685 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-21 12:27:33.308 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:1181 | 过滤了 4 个系统字段
2025-07-21 12:27:33.308 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5172 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 12:27:33.324 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 12:27:33.324 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 12:27:33.324 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 12:27:33.324 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 12:27:33.340 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 12:27:33.340 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 12:27:33.340 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 12:27:33.340 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 12:27:33.340 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 12:27:33.340 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 12:27:33.340 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 12:27:33.340 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 12:27:33.340 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 12:27:33.340 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 12:27:33.355 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 12:27:33.355 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 12:27:33.371 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 12:27:33.387 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 62.7ms
2025-07-21 12:27:33.387 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 12:27:33.387 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 12:27:33.402 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 降序
2025-07-21 12:27:33.402 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 12:27:33.418 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 50
2025-07-21 12:27:33.418 | INFO     | src.gui.widgets.pagination_widget:set_current_page:330 | 页码切换到: 1
2025-07-21 12:27:33.418 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 12:27:33.418 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3028 | 🔧 [分页修复] 数据更新事件设置总记录数: 1396
2025-07-21 12:27:33.418 | INFO     | src.gui.widgets.pagination_widget:set_current_page:330 | 页码切换到: 2
2025-07-21 12:27:33.418 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3033 | 🔧 [分页修复] 数据更新事件设置当前页: 2
2025-07-21 12:27:33.418 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3039 | 🔧 [分页修复] 分页状态: 当前页=2, 总页数=28, 总记录数=1396
2025-07-21 12:27:33.418 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3045 | 🔧 [分页修复] 按钮可用性: 下一页=True, 上一页=True
2025-07-21 12:27:33.449 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3823 | 🔧 [紧急修复] 新架构分页: 第1页
2025-07-21 12:27:33.449 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3835 | 新架构分页时应用排序: 1列
2025-07-21 12:27:33.449 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 1
2025-07-21 12:27:33.449 | INFO     | src.core.unified_data_request_manager:request_table_data:200 | 开始处理数据请求: salary_data_2025_07_active_employees, 类型: initial_load
2025-07-21 12:27:33.464 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 12:27:33.464 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 12:27:33.464 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_07_active_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=1列
2025-07-21 12:27:33.464 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:516 | 🔧 [排序修复] 准备排序列: grade_salary_2025
2025-07-21 12:27:33.464 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 12:27:33.464 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:523 | 🔧 [排序修复] 列 'grade_salary_2025' 验证通过，开始排序
2025-07-21 12:27:33.464 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:532 | 🔧 [排序修复] 数值列排序: grade_salary_2025 DESC
2025-07-21 12:27:33.464 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:539 | 🔧 [排序修复] 添加排序子句: CAST("grade_salary_2025" AS REAL) DESC
2025-07-21 12:27:33.464 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | 🔧 [P0-CRITICAL修复] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_07_active_employees" ORDER BY CAST("grade_salary_2025" AS REAL) DESC LIMIT 50 OFFSET 0
2025-07-21 12:27:33.480 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:557 | 🔧 [P0-CRITICAL修复] 数据库查询返回的前5个工号: ['19850005.0', '19860112.0', '19870288.0', '19870673.0', '19870883.0']
2025-07-21 12:27:33.480 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:566 | 🔧 [P0-CRITICAL修复] 查询结果中 grade_salary_2025 的前10个值: [6071.0, 5441.0, 5081.0, 4931.0, 4781.0, 4631.0, 4631.0, 4631.0, 4631.0, 4481.0]
2025-07-21 12:27:33.480 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:569 | 成功从表 salary_data_2025_07_active_employees 获取第1页数据（含排序）: 50 行，总计1396行
2025-07-21 12:27:33.480 | INFO     | src.core.unified_data_request_manager:request_table_data:249 | 数据请求处理完成: 28字段, 50行, 耗时31.4ms
2025-07-21 12:27:33.496 | INFO     | src.services.table_data_service:load_table_data:347 | 🎯 [统一格式化] 数据格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 12:27:33.496 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:2973 | 🆕 接收到新架构数据更新事件: salary_data_2025_07_active_employees
2025-07-21 12:27:33.496 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:2998 | 数据内容: 50行 x 28列
2025-07-21 12:27:33.496 | INFO     | src.gui.prototype.prototype_main_window:set_data:685 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-21 12:27:33.496 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:1181 | 过滤了 4 个系统字段
2025-07-21 12:27:33.496 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5172 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 12:27:33.496 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 12:27:33.496 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 12:27:33.511 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 12:27:33.511 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 12:27:33.511 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 12:27:33.511 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 12:27:33.511 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 12:27:33.511 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 12:27:33.511 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 12:27:33.511 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 12:27:33.527 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 12:27:33.527 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 12:27:33.527 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 12:27:33.527 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 12:27:33.527 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 12:27:33.527 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 12:27:33.527 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 12:27:33.543 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 47.0ms
2025-07-21 12:27:33.543 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 12:27:33.543 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 12:27:33.543 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 降序
2025-07-21 12:27:33.543 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 12:27:33.543 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 50
2025-07-21 12:27:33.543 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3823 | 🔧 [紧急修复] 新架构分页: 第1页
2025-07-21 12:27:33.543 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3835 | 新架构分页时应用排序: 1列
2025-07-21 12:27:33.558 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 1
2025-07-21 12:27:33.558 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5172 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 12:27:33.574 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 12:27:33.574 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 12:27:33.574 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 12:27:33.574 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 12:27:33.590 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 12:27:33.590 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 12:27:33.590 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 12:27:33.590 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 12:27:33.590 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 12:27:33.590 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 12:27:33.590 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 12:27:33.590 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 12:27:33.590 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 12:27:33.590 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 12:27:33.590 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 12:27:33.605 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 12:27:33.605 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 12:27:33.621 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 46.6ms
2025-07-21 12:27:33.621 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 12:27:33.621 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 12:27:33.621 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 降序
2025-07-21 12:27:33.621 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 12:27:33.621 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 12:27:33.636 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3898 | 🔧 [紧急修复] 新架构分页成功: 第1页, 50行
2025-07-21 12:27:33.636 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3823 | 🔧 [紧急修复] 新架构分页: 第1页
2025-07-21 12:27:33.636 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3835 | 新架构分页时应用排序: 1列
2025-07-21 12:27:33.636 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 1
2025-07-21 12:27:33.652 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5172 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 12:27:33.652 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 12:27:33.652 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 12:27:33.668 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 12:27:33.668 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 12:27:33.668 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 12:27:33.668 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 12:27:33.668 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 12:27:33.668 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 12:27:33.668 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 12:27:33.668 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 12:27:33.668 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 12:27:33.668 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 12:27:33.668 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 12:27:33.668 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 12:27:33.683 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 12:27:33.683 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 12:27:33.683 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 12:27:33.699 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 47.0ms
2025-07-21 12:27:33.699 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 12:27:33.699 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 12:27:33.699 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 降序
2025-07-21 12:27:33.699 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 12:27:33.715 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 12:27:33.715 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3898 | 🔧 [紧急修复] 新架构分页成功: 第1页, 50行
2025-07-21 12:27:33.715 | INFO     | src.gui.widgets.pagination_widget:set_current_page:330 | 页码切换到: 1
2025-07-21 12:27:33.715 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3039 | 🔧 [分页修复] 分页状态: 当前页=1, 总页数=28, 总记录数=1396
2025-07-21 12:27:33.715 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3045 | 🔧 [分页修复] 按钮可用性: 下一页=True, 上一页=False
2025-07-21 12:27:33.715 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5172 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 12:27:33.730 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 12:27:33.730 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 12:27:33.730 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 12:27:33.746 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 12:27:33.746 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 12:27:33.746 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 12:27:33.746 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 12:27:33.746 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 12:27:33.746 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 12:27:33.746 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 12:27:33.746 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 12:27:33.746 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 12:27:33.746 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 12:27:33.746 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 12:27:33.746 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 12:27:33.746 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 12:27:33.746 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 12:27:33.777 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 46.5ms
2025-07-21 12:27:33.777 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 12:27:33.777 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 12:27:33.777 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 降序
2025-07-21 12:27:33.777 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 12:27:33.777 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 12:27:33.777 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3898 | 🔧 [紧急修复] 新架构分页成功: 第1页, 50行
2025-07-21 12:27:33.777 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3823 | 🔧 [紧急修复] 新架构分页: 第1页
2025-07-21 12:27:33.777 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3835 | 新架构分页时应用排序: 1列
2025-07-21 12:27:33.777 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 1
2025-07-21 12:27:33.793 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5172 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 12:27:33.793 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 12:27:33.793 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 12:27:33.808 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 12:27:33.808 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 12:27:33.808 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 12:27:33.808 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 12:27:33.808 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 12:27:33.808 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 12:27:33.824 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 12:27:33.824 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 12:27:33.824 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 12:27:33.824 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 12:27:33.824 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 12:27:33.824 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 12:27:33.824 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 12:27:33.824 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 12:27:33.824 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 12:27:33.918 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 125.3ms
2025-07-21 12:27:33.918 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 12:27:33.918 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 12:27:33.918 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 降序
2025-07-21 12:27:33.918 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 12:27:33.918 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 12:27:33.918 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3898 | 🔧 [紧急修复] 新架构分页成功: 第1页, 50行
2025-07-21 12:27:33.918 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3823 | 🔧 [紧急修复] 新架构分页: 第2页
2025-07-21 12:27:33.918 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3835 | 新架构分页时应用排序: 1列
2025-07-21 12:27:33.933 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 2
2025-07-21 12:27:33.933 | INFO     | src.core.unified_data_request_manager:request_table_data:200 | 开始处理数据请求: salary_data_2025_07_active_employees, 类型: initial_load
2025-07-21 12:27:33.933 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 12:27:33.933 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 12:27:33.933 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_07_active_employees 分页获取数据（支持排序）: 第2页, 每页50条, 排序=1列
2025-07-21 12:27:33.933 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:516 | 🔧 [排序修复] 准备排序列: grade_salary_2025
2025-07-21 12:27:33.933 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 12:27:33.933 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:523 | 🔧 [排序修复] 列 'grade_salary_2025' 验证通过，开始排序
2025-07-21 12:27:33.933 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:532 | 🔧 [排序修复] 数值列排序: grade_salary_2025 DESC
2025-07-21 12:27:33.933 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:539 | 🔧 [排序修复] 添加排序子句: CAST("grade_salary_2025" AS REAL) DESC
2025-07-21 12:27:33.933 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | 🔧 [P0-CRITICAL修复] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_07_active_employees" ORDER BY CAST("grade_salary_2025" AS REAL) DESC LIMIT 50 OFFSET 50
2025-07-21 12:27:33.949 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:557 | 🔧 [P0-CRITICAL修复] 数据库查询返回的前5个工号: ['19881135.0', '19911130.0', '19901013.0', '19891097.0', '19880780.0']
2025-07-21 12:27:33.949 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:566 | 🔧 [P0-CRITICAL修复] 查询结果中 grade_salary_2025 的前10个值: [3921.0, 3921.0, 3921.0, 3921.0, 3921.0, 3921.0, 3921.0, 3921.0, 3921.0, 3921.0]
2025-07-21 12:27:33.949 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:569 | 成功从表 salary_data_2025_07_active_employees 获取第2页数据（含排序）: 50 行，总计1396行
2025-07-21 12:27:33.949 | INFO     | src.core.unified_data_request_manager:request_table_data:249 | 数据请求处理完成: 28字段, 50行, 耗时15.7ms
2025-07-21 12:27:33.949 | INFO     | src.services.table_data_service:load_table_data:347 | 🎯 [统一格式化] 数据格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 12:27:33.949 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:2973 | 🆕 接收到新架构数据更新事件: salary_data_2025_07_active_employees
2025-07-21 12:27:33.949 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:2998 | 数据内容: 50行 x 28列
2025-07-21 12:27:33.964 | INFO     | src.gui.prototype.prototype_main_window:set_data:685 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-21 12:27:33.964 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:1181 | 过滤了 4 个系统字段
2025-07-21 12:27:33.964 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5172 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 12:27:33.964 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 12:27:33.980 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 12:27:33.980 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 12:27:33.980 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 12:27:33.980 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 12:27:33.980 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 12:27:33.980 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 12:27:33.980 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 12:27:33.996 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 12:27:33.996 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 12:27:33.996 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 12:27:33.996 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 12:27:33.996 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 12:27:33.996 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 12:27:33.996 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 12:27:33.996 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 12:27:33.996 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 12:27:34.027 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 62.7ms
2025-07-21 12:27:34.027 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 12:27:34.027 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 12:27:34.027 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 降序
2025-07-21 12:27:34.027 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 12:27:34.027 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 50
2025-07-21 12:27:34.027 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 12:27:34.027 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3028 | 🔧 [分页修复] 数据更新事件设置总记录数: 1396
2025-07-21 12:27:34.027 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3823 | 🔧 [紧急修复] 新架构分页: 第2页
2025-07-21 12:27:34.027 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3835 | 新架构分页时应用排序: 1列
2025-07-21 12:27:34.027 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 2
2025-07-21 12:27:34.043 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5172 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 12:27:34.043 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 12:27:34.059 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 12:27:34.059 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 12:27:34.059 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 12:27:34.059 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 12:27:34.059 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 12:27:34.059 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 12:27:34.074 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 12:27:34.074 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 12:27:34.074 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 12:27:34.074 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 12:27:34.074 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 12:27:34.074 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 12:27:34.074 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 12:27:34.074 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 12:27:34.074 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 12:27:34.074 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 12:27:34.105 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 62.2ms
2025-07-21 12:27:34.105 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 12:27:34.105 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 12:27:34.105 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 降序
2025-07-21 12:27:34.105 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 12:27:34.105 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 12:27:34.105 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3898 | 🔧 [紧急修复] 新架构分页成功: 第2页, 50行
2025-07-21 12:27:34.105 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3823 | 🔧 [紧急修复] 新架构分页: 第2页
2025-07-21 12:27:34.105 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3835 | 新架构分页时应用排序: 1列
2025-07-21 12:27:34.105 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 2
2025-07-21 12:27:34.121 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5172 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 12:27:34.136 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 12:27:34.136 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 12:27:34.136 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 12:27:34.136 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 12:27:34.136 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 12:27:34.136 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 12:27:34.136 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 12:27:34.136 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 12:27:34.152 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 12:27:34.152 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 12:27:34.152 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 12:27:34.152 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 12:27:34.152 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 12:27:34.152 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 12:27:34.152 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 12:27:34.152 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 12:27:34.152 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 12:27:34.183 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 62.6ms
2025-07-21 12:27:34.183 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 12:27:34.183 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 12:27:34.183 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 降序
2025-07-21 12:27:34.183 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 12:27:34.183 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 12:27:34.183 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3898 | 🔧 [紧急修复] 新架构分页成功: 第2页, 50行
2025-07-21 12:27:34.183 | INFO     | src.gui.widgets.pagination_widget:set_current_page:330 | 页码切换到: 2
2025-07-21 12:27:34.183 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3033 | 🔧 [分页修复] 数据更新事件设置当前页: 2
2025-07-21 12:27:34.183 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3039 | 🔧 [分页修复] 分页状态: 当前页=2, 总页数=28, 总记录数=1396
2025-07-21 12:27:34.183 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3045 | 🔧 [分页修复] 按钮可用性: 下一页=True, 上一页=True
2025-07-21 12:27:34.199 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5172 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 12:27:34.199 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 12:27:34.199 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 12:27:34.215 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 12:27:34.215 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 12:27:34.215 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 12:27:34.215 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 12:27:34.215 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 12:27:34.215 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 12:27:34.215 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 12:27:34.215 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 12:27:34.230 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 12:27:34.230 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 12:27:34.230 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 12:27:34.230 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 12:27:34.230 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 12:27:34.230 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 12:27:34.230 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 12:27:34.261 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 62.1ms
2025-07-21 12:27:34.261 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 12:27:34.261 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 12:27:34.261 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 降序
2025-07-21 12:27:34.261 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 12:27:34.261 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 12:27:34.261 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3898 | 🔧 [紧急修复] 新架构分页成功: 第2页, 50行
2025-07-21 12:27:34.261 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3823 | 🔧 [紧急修复] 新架构分页: 第2页
2025-07-21 12:27:34.261 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3835 | 新架构分页时应用排序: 1列
2025-07-21 12:27:34.261 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 2
2025-07-21 12:27:34.277 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5172 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 12:27:34.277 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 12:27:34.277 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 12:27:34.293 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 12:27:34.293 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 12:27:34.293 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 12:27:34.293 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 12:27:34.293 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 12:27:34.293 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 12:27:34.293 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 12:27:34.293 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 12:27:34.308 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 12:27:34.308 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 12:27:34.308 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 12:27:34.308 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 12:27:34.308 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 12:27:34.308 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 12:27:34.308 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 12:27:34.324 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 47.0ms
2025-07-21 12:27:34.340 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 12:27:34.340 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 12:27:34.340 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 降序
2025-07-21 12:27:34.340 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 12:27:34.340 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 12:27:34.340 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3898 | 🔧 [紧急修复] 新架构分页成功: 第2页, 50行
2025-07-21 12:27:34.839 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1564 | 列宽设置已保存: salary_data_2025_07_active_employees (24 列)
2025-07-21 12:27:39.324 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3823 | 🔧 [紧急修复] 新架构分页: 第3页
2025-07-21 12:27:39.324 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3835 | 新架构分页时应用排序: 1列
2025-07-21 12:27:39.324 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 3
2025-07-21 12:27:39.324 | INFO     | src.core.unified_data_request_manager:request_table_data:200 | 开始处理数据请求: salary_data_2025_07_active_employees, 类型: initial_load
2025-07-21 12:27:39.324 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 12:27:39.324 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 12:27:39.324 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_07_active_employees 分页获取数据（支持排序）: 第3页, 每页50条, 排序=1列
2025-07-21 12:27:39.324 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:516 | 🔧 [排序修复] 准备排序列: grade_salary_2025
2025-07-21 12:27:39.324 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | 🔧 [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_07_active_employees' 中（类型: REAL）
2025-07-21 12:27:39.324 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:523 | 🔧 [排序修复] 列 'grade_salary_2025' 验证通过，开始排序
2025-07-21 12:27:39.341 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:532 | 🔧 [排序修复] 数值列排序: grade_salary_2025 DESC
2025-07-21 12:27:39.341 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:539 | 🔧 [排序修复] 添加排序子句: CAST("grade_salary_2025" AS REAL) DESC
2025-07-21 12:27:39.341 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | 🔧 [P0-CRITICAL修复] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_07_active_employees" ORDER BY CAST("grade_salary_2025" AS REAL) DESC LIMIT 50 OFFSET 100
2025-07-21 12:27:39.355 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:557 | 🔧 [P0-CRITICAL修复] 数据库查询返回的前5个工号: ['19840232.0', '19870226.0', '19920265.0', '19890293.0', '19921156.0']
2025-07-21 12:27:39.355 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:566 | 🔧 [P0-CRITICAL修复] 查询结果中 grade_salary_2025 的前10个值: [3641.0, 3641.0, 3641.0, 3641.0, 3641.0, 3641.0, 3641.0, 3641.0, 3641.0, 3641.0]
2025-07-21 12:27:39.355 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:569 | 成功从表 salary_data_2025_07_active_employees 获取第3页数据（含排序）: 50 行，总计1396行
2025-07-21 12:27:39.355 | INFO     | src.core.unified_data_request_manager:request_table_data:249 | 数据请求处理完成: 28字段, 50行, 耗时31.4ms
2025-07-21 12:27:39.355 | INFO     | src.services.table_data_service:load_table_data:347 | 🎯 [统一格式化] 数据格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 12:27:39.355 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:2973 | 🆕 接收到新架构数据更新事件: salary_data_2025_07_active_employees
2025-07-21 12:27:39.355 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:2998 | 数据内容: 50行 x 28列
2025-07-21 12:27:39.355 | INFO     | src.gui.prototype.prototype_main_window:set_data:685 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-21 12:27:39.355 | INFO     | src.gui.prototype.prototype_main_window:_apply_system_field_filtering:1181 | 过滤了 4 个系统字段
2025-07-21 12:27:39.355 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5172 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 12:27:39.371 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 12:27:39.371 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 12:27:39.386 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 12:27:39.386 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 12:27:39.386 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 12:27:39.386 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 12:27:39.386 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 12:27:39.386 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 12:27:39.386 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 12:27:39.386 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 12:27:39.386 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 12:27:39.386 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 12:27:39.386 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 12:27:39.386 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 12:27:39.386 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 12:27:39.386 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 12:27:39.386 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 12:27:39.418 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 47.0ms
2025-07-21 12:27:39.418 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 12:27:39.418 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 12:27:39.418 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 降序
2025-07-21 12:27:39.418 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 12:27:39.418 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 50
2025-07-21 12:27:39.418 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3823 | 🔧 [紧急修复] 新架构分页: 第1页
2025-07-21 12:27:39.418 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3835 | 新架构分页时应用排序: 1列
2025-07-21 12:27:39.418 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 1
2025-07-21 12:27:39.418 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5172 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 12:27:39.433 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 12:27:39.433 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 12:27:39.449 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 12:27:39.449 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 12:27:39.449 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 12:27:39.449 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 12:27:39.449 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 12:27:39.449 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 12:27:39.449 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 12:27:39.449 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 12:27:39.449 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 12:27:39.449 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 12:27:39.449 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 12:27:39.449 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 12:27:39.449 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 12:27:39.449 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 12:27:39.449 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 12:27:39.480 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 46.5ms
2025-07-21 12:27:39.480 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 12:27:39.480 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 12:27:39.480 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 降序
2025-07-21 12:27:39.480 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 12:27:39.480 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 12:27:39.480 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3898 | 🔧 [紧急修复] 新架构分页成功: 第1页, 50行
2025-07-21 12:27:39.480 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3823 | 🔧 [紧急修复] 新架构分页: 第1页
2025-07-21 12:27:39.480 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3835 | 新架构分页时应用排序: 1列
2025-07-21 12:27:39.480 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 1
2025-07-21 12:27:39.480 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5172 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 12:27:39.496 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 12:27:39.496 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 12:27:39.511 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 12:27:39.511 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 12:27:39.511 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 12:27:39.511 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 12:27:39.511 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 12:27:39.511 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 12:27:39.511 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 12:27:39.511 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 12:27:39.511 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 12:27:39.511 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 12:27:39.511 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 12:27:39.511 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 12:27:39.511 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 12:27:39.511 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 12:27:39.511 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 12:27:39.527 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 31.3ms
2025-07-21 12:27:39.527 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 12:27:39.527 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 12:27:39.527 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 降序
2025-07-21 12:27:39.527 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 12:27:39.527 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 12:27:39.543 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3898 | 🔧 [紧急修复] 新架构分页成功: 第1页, 50行
2025-07-21 12:27:39.543 | INFO     | src.gui.widgets.pagination_widget:set_current_page:330 | 页码切换到: 1
2025-07-21 12:27:39.543 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3823 | 🔧 [紧急修复] 新架构分页: 第3页
2025-07-21 12:27:39.543 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3835 | 新架构分页时应用排序: 1列
2025-07-21 12:27:39.543 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 3
2025-07-21 12:27:39.543 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5172 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 12:27:39.543 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 12:27:39.543 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 12:27:39.558 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 12:27:39.558 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 12:27:39.558 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 12:27:39.558 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 12:27:39.558 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 12:27:39.558 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 12:27:39.558 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 12:27:39.558 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 12:27:39.558 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 12:27:39.574 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 12:27:39.574 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 12:27:39.574 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 12:27:39.574 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 12:27:39.574 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 12:27:39.574 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 12:27:39.590 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 46.5ms
2025-07-21 12:27:39.590 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 12:27:39.590 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 12:27:39.590 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 降序
2025-07-21 12:27:39.590 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 12:27:39.590 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 12:27:39.590 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3898 | 🔧 [紧急修复] 新架构分页成功: 第3页, 50行
2025-07-21 12:27:39.590 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3823 | 🔧 [紧急修复] 新架构分页: 第3页
2025-07-21 12:27:39.590 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3835 | 新架构分页时应用排序: 1列
2025-07-21 12:27:39.590 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 3
2025-07-21 12:27:39.605 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5172 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 12:27:39.605 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 12:27:39.605 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 12:27:39.621 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 12:27:39.621 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 12:27:39.621 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 12:27:39.621 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 12:27:39.621 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 12:27:39.621 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 12:27:39.621 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 12:27:39.621 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 12:27:39.621 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 12:27:39.621 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 12:27:39.621 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 12:27:39.621 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 12:27:39.621 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 12:27:39.621 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 12:27:39.621 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 12:27:39.652 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 47.0ms
2025-07-21 12:27:39.652 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 12:27:39.652 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 12:27:39.652 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 降序
2025-07-21 12:27:39.652 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 12:27:39.652 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 12:27:39.652 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3898 | 🔧 [紧急修复] 新架构分页成功: 第3页, 50行
2025-07-21 12:27:39.652 | INFO     | src.gui.widgets.pagination_widget:set_current_page:330 | 页码切换到: 3
2025-07-21 12:27:39.652 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3033 | 🔧 [分页修复] 数据更新事件设置当前页: 3
2025-07-21 12:27:39.652 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3039 | 🔧 [分页修复] 分页状态: 当前页=3, 总页数=28, 总记录数=1396
2025-07-21 12:27:39.652 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3045 | 🔧 [分页修复] 按钮可用性: 下一页=True, 上一页=True
2025-07-21 12:27:39.652 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5172 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 12:27:39.668 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 12:27:39.668 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 12:27:39.683 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 12:27:39.683 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 12:27:39.683 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 12:27:39.683 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 12:27:39.683 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 12:27:39.683 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 12:27:39.683 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 12:27:39.683 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 12:27:39.683 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 12:27:39.683 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 12:27:39.683 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 12:27:39.683 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 12:27:39.683 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 12:27:39.683 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 12:27:39.683 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 12:27:39.699 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 30.8ms
2025-07-21 12:27:39.699 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 12:27:39.699 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 12:27:39.714 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 降序
2025-07-21 12:27:39.714 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 12:27:39.714 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 12:27:39.714 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3898 | 🔧 [紧急修复] 新架构分页成功: 第3页, 50行
2025-07-21 12:27:39.714 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3823 | 🔧 [紧急修复] 新架构分页: 第3页
2025-07-21 12:27:39.714 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3835 | 新架构分页时应用排序: 1列
2025-07-21 12:27:39.714 | INFO     | src.services.table_data_service:load_table_data:296 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 3
2025-07-21 12:27:39.714 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:5172 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-21 12:27:39.714 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:3953 | 最大可见行数已更新: 50 -> 50
2025-07-21 12:27:39.714 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4006 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-21 12:27:39.731 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2362 | 表格格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-21 12:27:39.731 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-07-21 12:27:39.731 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-07-21 12:27:39.731 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-07-21 12:27:39.731 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-07-21 12:27:39.731 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-07-21 12:27:39.731 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:473 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-21 12:27:39.731 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:440 | 表格数据已设置: 50 行, 24 列
2025-07-21 12:27:39.731 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=工号, 原值=19990089, 类型=<class 'str'>
2025-07-21 12:27:39.731 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=工号, 原值=19990089, 格式化后=19990089
2025-07-21 12:27:39.731 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2719 | 开始格式化ID字段: 列=人员类别代码, 原值=01, 类型=<class 'str'>
2025-07-21 12:27:39.746 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2726 | ID字段格式化完成: 列=人员类别代码, 原值=01, 格式化后=01
2025-07-21 12:27:39.746 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=0, 列=2025年薪级工资, 原值=2375.0 (<class 'float'>), 格式化后=2375.00
2025-07-21 12:27:39.746 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=1, 列=2025年薪级工资, 原值=1696.0 (<class 'float'>), 格式化后=1696.00
2025-07-21 12:27:39.746 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_populate_visible_data:2730 | 行=2, 列=2025年薪级工资, 原值=1427.0 (<class 'float'>), 格式化后=1427.00
2025-07-21 12:27:39.761 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2420 | 表格数据设置完成: 50 行, 耗时: 47.0ms
2025-07-21 12:27:39.761 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6302 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-21 12:27:39.761 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6312 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-07-21 12:27:39.761 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:6313 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 降序
2025-07-21 12:27:39.761 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1615 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-21 12:27:39.761 | INFO     | src.gui.widgets.pagination_widget:set_total_records:308 | 总记录数设置为: 1396
2025-07-21 12:27:39.761 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:3898 | 🔧 [紧急修复] 新架构分页成功: 第3页, 50行
2025-07-21 12:27:39.761 | INFO     | src.gui.widgets.pagination_widget:set_current_page:330 | 页码切换到: 3
2025-07-21 12:27:40.261 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1564 | 列宽设置已保存: salary_data_2025_07_active_employees (24 列)
2025-07-21 12:28:18.891 | INFO     | __main__:main:427 | 应用程序正常退出
