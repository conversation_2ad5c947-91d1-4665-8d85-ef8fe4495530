#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
格式化回归测试
🧪 [回归测试] 确保修复后所有功能正常工作

验证点：
1. 数据显示不再为0.00
2. 排序功能正常
3. 格式化状态跟踪
4. 配置化管理

创建时间: 2025-07-19
作者: 薪资系统重构团队
"""

import sys
from pathlib import Path
import pandas as pd

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_no_zero_display():
    """测试数据不再显示为0.00"""
    print("🧪 测试1: 验证数据不再显示为0.00")
    
    from src.modules.format_management.master_format_manager import get_master_format_manager
    
    master_formatter = get_master_format_manager()
    
    # 测试已格式化的数据
    test_value = "¥3,266.00"
    result = master_formatter.format_display_value(test_value, '2025年薪级工资')
    
    if result == "¥3,266.00":
        print("  ✅ 已格式化数据正确显示")
        return True
    else:
        print(f"  ❌ 已格式化数据显示错误: {result}")
        return False

def test_format_state_tracking():
    """测试格式化状态跟踪"""
    print("🧪 测试2: 验证格式化状态跟踪")
    
    from src.modules.format_management.master_format_manager import get_master_format_manager
    
    master_formatter = get_master_format_manager()
    
    # 创建测试数据
    test_data = pd.DataFrame({
        '工号': ['EMP001', 'EMP002'],
        '2025年岗位工资': [5000.0, 6000.0]
    })
    
    # 首次格式化
    formatted_data1 = master_formatter.format_table_data(test_data, 'active_employees')
    
    # 检查状态跟踪
    if master_formatter._format_state_tracker.is_formatted(formatted_data1):
        print("  ✅ 格式化状态正确跟踪")
        return True
    else:
        print("  ❌ 格式化状态跟踪失败")
        return False

def test_config_system():
    """测试配置系统"""
    print("🧪 测试3: 验证配置系统")
    
    from src.modules.format_management.master_format_manager import get_master_format_manager
    
    master_formatter = get_master_format_manager()
    
    # 测试字段类型检测
    currency_type = master_formatter.get_field_type('2025年岗位工资')
    string_type = master_formatter.get_field_type('工号')
    
    if currency_type == 'currency' and string_type == 'string':
        print("  ✅ 配置系统字段类型检测正常")
        return True
    else:
        print(f"  ❌ 配置系统异常: 岗位工资={currency_type}, 工号={string_type}")
        return False

def test_performance_improvement():
    """测试性能提升"""
    print("🧪 测试4: 验证性能提升")
    
    import time
    from src.modules.format_management.master_format_manager import get_master_format_manager
    
    master_formatter = get_master_format_manager()
    
    # 创建测试数据
    test_data = pd.DataFrame({
        '工号': [f'EMP{i:03d}' for i in range(100)],
        '2025年岗位工资': [5000.0 + i for i in range(100)]
    })
    
    # 清理缓存
    master_formatter.clear_cache()
    
    # 首次格式化
    start_time = time.time()
    formatted_data = master_formatter.format_table_data(test_data, 'active_employees')
    first_time = (time.time() - start_time) * 1000
    
    # 重复格式化（应该很快）
    start_time = time.time()
    master_formatter.format_table_data(formatted_data, 'active_employees')
    repeat_time = (time.time() - start_time) * 1000
    
    performance_ok = repeat_time < 5.0  # 重复格式化应该在5ms内
    
    if performance_ok:
        print(f"  ✅ 性能提升验证通过: 重复格式化={repeat_time:.2f}ms < 5ms")
        return True
    else:
        print(f"  ❌ 性能提升不达标: 重复格式化={repeat_time:.2f}ms >= 5ms")
        return False

def main():
    """主测试函数"""
    print("🔧 薪资系统格式化回归测试")
    print("=" * 40)
    
    tests = [
        test_no_zero_display,
        test_format_state_tracking,
        test_config_system,
        test_performance_improvement
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"  ❌ 测试执行失败: {e}")
    
    print("\n📊 测试总结")
    print("=" * 20)
    success_rate = passed / total * 100
    
    if passed == total:
        print(f"🎉 所有测试通过！({passed}/{total}, {success_rate:.1f}%)")
        print("✅ 格式化重构优化成功完成")
        return True
    else:
        print(f"⚠️ 部分测试失败 ({passed}/{total}, {success_rate:.1f}%)")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)