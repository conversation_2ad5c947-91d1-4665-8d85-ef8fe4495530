#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主格式化管理器 - 统一所有格式化操作
🎯 [统一格式化] 解决多层格式化冲突，建立单一格式化流程

本模块实现了统一的格式化管理，解决数据被多次格式化导致的问题。
主要功能：
1. 格式化状态跟踪
2. 单一格式化入口
3. 格式化缓存管理
4. 配置化格式化规则

作者: 薪资系统重构团队
创建时间: 2025-07-19
版本: v1.0.0
"""

import hashlib
import time
import json
from typing import Any, Dict, List, Optional, Union
import pandas as pd
from pathlib import Path

from src.utils.log_config import setup_logger


class FormatStateTracker:
    """
    格式化状态跟踪器 - 跟踪数据格式化状态
    🚨 [关键组件] 防止重复格式化
    """
    
    def __init__(self):
        self.logger = setup_logger(self.__class__.__name__)
        self._formatted_data_ids = set()
    
    def is_formatted(self, data: Union[pd.DataFrame, List[Dict]]) -> bool:
        """检查数据是否已格式化"""
        try:
            # 检查DataFrame属性标记
            if isinstance(data, pd.DataFrame):
                if hasattr(data, 'attrs') and data.attrs.get('formatted'):
                    return True
                
                # 检查数据特征
                if self._check_dataframe_characteristics(data):
                    return True
            
            # 检查列表数据特征
            elif isinstance(data, list) and data:
                if self._check_list_characteristics(data):
                    return True
            
            # 检查ID缓存
            data_id = self._generate_data_id(data)
            return data_id in self._formatted_data_ids
            
        except Exception as e:
            self.logger.warning(f"格式化状态检查失败: {e}")
            return False
    
    def mark_formatted(self, data: Union[pd.DataFrame, List[Dict]]):
        """标记数据已格式化"""
        try:
            # 设置DataFrame属性标记
            if isinstance(data, pd.DataFrame) and hasattr(data, 'attrs'):
                data.attrs['formatted'] = True
            
            # 添加到ID缓存
            data_id = self._generate_data_id(data)
            self._formatted_data_ids.add(data_id)
            
            self.logger.debug(f"数据已标记为格式化: {data_id[:8]}...")
            
        except Exception as e:
            self.logger.warning(f"标记格式化状态失败: {e}")
    
    def _check_dataframe_characteristics(self, data: pd.DataFrame) -> bool:
        """检查DataFrame特征判断是否已格式化"""
        # 🔧 [P0-CRITICAL] 修复DataFrame条件判断错误
        if data.empty:  # 使用.empty而不是len()判断
            return False
        
        try:
            # 检查货币字段特征
            for column in data.columns:
                if '工资' in column or '津贴' in column or '补贴' in column:
                    # 🔧 [P0-CRITICAL] 安全检查列数据
                    if not data[column].empty:
                        sample_value = data.iloc[0][column]
                        if isinstance(sample_value, str) and '¥' in sample_value:
                            return True
            return False
            
        except Exception:
            return False
    
    def _check_list_characteristics(self, data: List[Dict]) -> bool:
        """检查列表数据特征判断是否已格式化"""
        if not data:
            return False
        
        try:
            first_row = data[0]
            for key, value in first_row.items():
                if '工资' in key or '津贴' in key or '补贴' in key:
                    if isinstance(value, str) and '¥' in value:
                        return True
            return False
            
        except Exception:
            return False
    
    def _generate_data_id(self, data: Union[pd.DataFrame, List[Dict]]) -> str:
        """生成数据唯一标识"""
        try:
            if isinstance(data, pd.DataFrame):
                data_str = f"{data.shape}_{list(data.columns)}_{str(data.iloc[0].values if len(data) > 0 else [])}"
            else:
                data_str = f"{len(data)}_{list(data[0].keys() if data else [])}_{str(data[0] if data else {})}"
            
            return hashlib.md5(data_str.encode()).hexdigest()
            
        except Exception:
            return hashlib.md5(str(time.time()).encode()).hexdigest()


class MasterFormatManager:
    """
    主格式化管理器 - 协调所有格式化操作
    🎯 [统一格式化] 建立单一、清晰的格式化流程
    """
    
    def __init__(self):
        self.logger = setup_logger(self.__class__.__name__)
        self._format_state_tracker = FormatStateTracker()
        self._format_cache = {}
        self._enabled_formatters = ['salary_data']  # 默认只启用SalaryDataFormatter
        
        # 延迟加载格式化器实例
        self._formatter_instances = {}
        
        # 🧹 [配置化管理] 加载格式化配置
        self._format_config = self._load_format_config()
        
        self.logger.info("🎯 [统一格式化] 主格式化管理器已初始化")
    
    def format_table_data(self, data: Union[pd.DataFrame, List[Dict]], table_type: str, 
                         force_reformat: bool = False) -> Union[pd.DataFrame, List[Dict]]:
        """
        统一的表格数据格式化入口
        🎯 [统一格式化] 防止重复格式化，提升性能
        
        Args:
            data: 原始数据
            table_type: 表格类型
            force_reformat: 是否强制重新格式化
            
        Returns:
            格式化后的数据
        """
        try:
            # 1. 检查数据是否已格式化
            if not force_reformat and self._format_state_tracker.is_formatted(data):
                self.logger.info("🎯 [统一格式化] 数据已格式化，跳过重复处理")
                return data
            
            # 2. 检查缓存
            cache_key = self._generate_cache_key(data, table_type)
            cached_result = self._format_cache.get(cache_key)
            if cached_result is not None and not force_reformat:
                self.logger.debug("🎯 [统一格式化] 使用缓存结果")
                return cached_result
            
            # 3. 执行格式化
            formatted_data = self._execute_formatting(data, table_type)
            
            # 4. 标记格式化状态
            self._format_state_tracker.mark_formatted(formatted_data)
            
            # 5. 缓存结果（限制缓存大小）
            if len(self._format_cache) < 100:  # 防止内存泄漏
                self._format_cache[cache_key] = formatted_data
            
            self.logger.info(f"🎯 [统一格式化] 数据格式化完成: {table_type}")
            return formatted_data
            
        except Exception as e:
            # 🔧 [P0-修复] 移除防护性掩盖代码，直接处理错误
            self.logger.error(f"🎯 [统一格式化] 格式化失败: {e}")
            # 清理缓存确保数据一致性
            self.clear_cache()
            return data  # 返回原始数据，不中断流程
    
    def format_display_value(self, value: Any, column_name: str) -> str:
        """
        格式化显示值
        🎯 [统一格式化] 用于单元格级别的格式化
        
        Args:
            value: 原始值
            column_name: 列名
            
        Returns:
            格式化后的字符串
        """
        try:
            # 🧹 [配置化管理] 根据配置确定字段类型并格式化
            field_type = self.get_field_type(column_name)
            
            if field_type == 'float':
                return self._format_float_value(value)
            elif field_type == 'string':
                return str(value) if value is not None else ""
            else:
                # 回退到SalaryDataFormatter
                formatter = self._get_salary_formatter()
                if formatter:
                    return formatter.format_display_value_by_table(value, column_name, 'active_employees')
                
                return str(value) if value is not None else ""
            
        except Exception as e:
            self.logger.warning(f"显示值格式化失败: {e}")
            return str(value) if value is not None else ""
    
    def _format_float_value(self, value: Any) -> str:
        """格式化浮点数值（两位小数，无符号）"""
        try:
            if value is None or value == '' or str(value).lower() in ['nan', 'none']:
                return "0.00"
            
            numeric_value = float(value)
            
            # 格式化为两位小数，不使用千分位分隔符，不加任何符号
            formatted_value = f"{numeric_value:.2f}"
            
            return formatted_value
            
        except (ValueError, TypeError):
            return "0.00"
    
    def _execute_formatting(self, data: Union[pd.DataFrame, List[Dict]], table_type: str) -> Union[pd.DataFrame, List[Dict]]:
        """执行具体的格式化逻辑"""
        try:
            # 根据配置选择格式化器
            if 'salary_data' in self._enabled_formatters:
                formatter = self._get_salary_formatter()
                if formatter:
                    if isinstance(data, pd.DataFrame):
                        return formatter.format_table_data(data, table_type)
                    else:
                        # 对于列表数据，先转为DataFrame处理
                        if isinstance(data, list) and len(data) > 0:
                            df = pd.DataFrame(data)
                            formatted_df = formatter.format_table_data(df, table_type)
                            return formatted_df.to_dict('records')
            
            return data
            
        except Exception as e:
            self.logger.error(f"格式化执行失败: {e}")
            return data
    
    def _get_salary_formatter(self):
        """获取SalaryDataFormatter实例"""
        if 'salary_data' not in self._formatter_instances:
            try:
                from src.modules.data_storage.salary_data_formatter import SalaryDataFormatter
                self._formatter_instances['salary_data'] = SalaryDataFormatter()
            except ImportError as e:
                self.logger.error(f"导入SalaryDataFormatter失败: {e}")
                return None
        
        return self._formatter_instances['salary_data']
    
    def _generate_cache_key(self, data: Union[pd.DataFrame, List[Dict]], table_type: str) -> str:
        """生成缓存键"""
        try:
            if isinstance(data, pd.DataFrame):
                data_hash = f"{data.shape}_{len(data.columns)}_{table_type}"
            else:
                data_hash = f"{len(data)}_{len(data[0]) if data else 0}_{table_type}"
            
            return hashlib.md5(data_hash.encode()).hexdigest()[:16]
            
        except Exception:
            return f"cache_{int(time.time())}"
    
    def clear_cache(self):
        """清理格式化缓存"""
        self._format_cache.clear()
        self.logger.info("🎯 [统一格式化] 格式化缓存已清理")
    
    def get_format_stats(self) -> Dict[str, Any]:
        """获取格式化统计信息"""
        return {
            'cache_size': len(self._format_cache),
            'formatted_data_count': len(self._format_state_tracker._formatted_data_ids),
            'enabled_formatters': self._enabled_formatters.copy(),
            'config_loaded': self._format_config is not None
        }
    
    def _load_format_config(self) -> Dict[str, Any]:
        """
        🧹 [配置化管理] 加载格式化配置文件
        """
        try:
            config_path = Path(__file__).parent.parent.parent.parent / "state" / "data" / "format_config.json"
            
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                self.logger.info(f"格式化配置加载成功: state/data/format_config.json")
                return config
            else:
                self.logger.warning(f"配置文件不存在: state/data/format_config.json")
                return self._create_default_config()
                
        except Exception as e:
            self.logger.error(f"配置加载失败: {e}")
            return self._create_default_config()
    
    def _create_default_config(self) -> Dict[str, Any]:
        """创建默认配置"""
        return {
            "defaults": {
                "float": {
                    "decimal_places": 2,
                    "zero_display": "0.00",
                    "error_display": "0.00"
                }
            },
            "tables": {
                "active_employees": {
                    "fields": {
                        "2025年岗位工资": {"type": "float"},
                        "2025年薪级工资": {"type": "float"},
                        "津贴": {"type": "float"}
                    }
                }
            }
        }
    
    def get_field_type(self, field_name: str, table_type: str = 'active_employees') -> str:
        """
        🧹 [配置化管理] 根据配置获取字段类型
        """
        try:
            table_config = self._format_config.get('tables', {}).get(table_type, {})
            field_config = table_config.get('fields', {}).get(field_name, {})
            
            if field_config.get('type'):
                return field_config['type']
            
            # 回退到模式匹配
            field_patterns = self._format_config.get('field_patterns', {})
            
            # 检查浮点数字段
            float_keywords = field_patterns.get('float_keywords', ['工资', '津贴', '补贴'])
            if any(keyword in field_name for keyword in float_keywords):
                return 'float'
            
            # 检查ID字段
            id_keywords = field_patterns.get('id_keywords', ['工号', '代码'])
            if any(keyword in field_name for keyword in id_keywords):
                return 'string'
            
            # 默认为字符串
            return 'string'
            
        except Exception as e:
            self.logger.warning(f"字段类型检测失败: {e}")
            return 'string'


# 全局单例实例
_master_format_manager = None

def get_master_format_manager() -> MasterFormatManager:
    """获取全局主格式化管理器单例"""
    global _master_format_manager
    if _master_format_manager is None:
        _master_format_manager = MasterFormatManager()
    return _master_format_manager