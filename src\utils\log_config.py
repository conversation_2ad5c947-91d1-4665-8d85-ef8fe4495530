"""
月度工资异动处理系统 - 日志配置模块

本模块提供统一的日志配置和管理功能，支持：
- 文件日志和控制台日志
- 日志级别控制
- 日志格式自定义
- 日志文件轮转
- 线程安全的日志记录

依赖库：
- loguru: 现代化日志库，替代标准logging
- pathlib: 路径处理
- os: 环境变量获取

创建时间: 2025-06-15 (CREATIVE阶段 Day 1)
更新时间: 2025-01-22 (REFLECT Day 2 - Logger类型修复)
"""

import os
import sys
from pathlib import Path
from typing import Optional, Union, Dict, Any, TYPE_CHECKING

if TYPE_CHECKING:
    from loguru import Logger
else:
    from loguru import logger
    Logger = type(logger)

from loguru import logger

# 移除默认handler，避免重复输出
logger.remove()

# 全局初始化标记，避免函数属性的类型问题
_logger_initialized: bool = False

# 日志目录路径
def get_log_directory() -> Path:
    """
    获取日志目录路径
    
    Returns:
        Path: 日志目录的绝对路径
    """
    # 获取项目根目录
    current_file = Path(__file__)
    project_root = current_file.parent.parent.parent  # 回到项目根目录
    log_dir = project_root / "logs"
    
    # 确保日志目录存在
    log_dir.mkdir(exist_ok=True)
    
    return log_dir

def setup_logger(
    module_name: Optional[str] = None,
    log_level: str = "INFO",
    log_to_file: bool = True,
    log_to_console: bool = True,
    log_file_name: Optional[str] = None,
    max_file_size: str = "10 MB",
    rotation_count: int = 5
) -> Logger:
    """
    配置日志系统，支持模块名作为第一个参数
    
    Args:
        module_name: 模块名称（如果提供，返回带有模块上下文的logger）
        log_level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_to_file: 是否输出到文件
        log_to_console: 是否输出到控制台
        log_file_name: 日志文件名，默认为salary_system.log
        max_file_size: 单个日志文件最大大小
        rotation_count: 保留的轮转文件数量
        
    Returns:
        配置好的logger实例
    """
    # 如果已经配置过，直接返回
    if _is_logger_configured(module_name):
        return logger.bind(module=module_name)
    
    level = log_level.upper()
    
    # 配置控制台和文件日志
    _configure_console_logging(log_to_console, level)
    _configure_file_logging(log_to_file, level, log_file_name, max_file_size, rotation_count)
    
    # 记录初始化信息
    _log_initialization_info(level, log_to_console, log_to_file, log_file_name)
    
    # 返回带模块上下文的logger（如果需要）
    return logger.bind(module=module_name) if module_name else logger

def _is_logger_configured(module_name: Optional[str]) -> bool:
    """
    检查logger是否已配置
    
    Args:
        module_name: 模块名称
        
    Returns:
        bool: 是否已配置
    """
    return hasattr(logger, '_core') and logger._core.handlers and module_name

def _configure_console_logging(log_to_console: bool, level: str) -> None:
    """
    配置控制台日志
    
    Args:
        log_to_console: 是否输出到控制台
        level: 日志级别
    """
    if not log_to_console:
        return
    
    console_format = _get_console_format()
    logger.add(
        sys.stdout,
        format=console_format,
        level=level,
        colorize=True
    )

def _configure_file_logging(log_to_file: bool, level: str, log_file_name: Optional[str], 
                           max_file_size: str, rotation_count: int) -> None:
    """
    配置文件日志
    
    Args:
        log_to_file: 是否输出到文件
        level: 日志级别
        log_file_name: 日志文件名
        max_file_size: 文件最大大小
        rotation_count: 轮转文件数量
    """
    if not log_to_file:
        return
    
    log_file_path = _get_log_file_path(log_file_name)
    file_format = _get_file_format()
    
    logger.add(
        str(log_file_path),
        format=file_format,
        level=level,
        rotation=max_file_size,
        retention=rotation_count,
        encoding="utf-8",
        backtrace=True,
        diagnose=True
    )

def _get_console_format() -> str:
    """获取控制台日志格式"""
    return (
        "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
        "<level>{message}</level>"
    )

def _get_file_format() -> str:
    """获取文件日志格式"""
    return (
        "{time:YYYY-MM-DD HH:mm:ss.SSS} | "
        "{level: <8} | "
        "{name}:{function}:{line} | "
        "{message}"
    )

def _get_log_file_path(log_file_name: Optional[str]) -> Path:
    """
    获取日志文件路径
    
    Args:
        log_file_name: 日志文件名
        
    Returns:
        Path: 日志文件完整路径
    """
    log_dir = get_log_directory()
    if log_file_name is None:
        log_file_name = "salary_system.log"
    return log_dir / log_file_name

def _log_initialization_info(level: str, log_to_console: bool, log_to_file: bool, 
                           log_file_name: Optional[str]) -> None:
    """
    记录日志系统初始化信息
    
    Args:
        level: 日志级别
        log_to_console: 是否输出到控制台
        log_to_file: 是否输出到文件
        log_file_name: 日志文件名
    """
    global _logger_initialized
    if _logger_initialized:
        return
    
    logger.info(f"日志系统初始化完成")
    logger.info(f"日志级别: {level}")
    logger.info(f"控制台输出: {log_to_console}")
    logger.info(f"文件输出: {log_to_file}")
    
    if log_to_file:
        log_file_path = _get_log_file_path(log_file_name)
        # 只显示相对路径，避免暴露系统路径
        relative_path = f"logs/{log_file_name or 'salary_system.log'}"
        logger.info(f"日志文件路径: {relative_path}")
    
    _logger_initialized = True

def get_module_logger(module_name: str) -> Logger:
    """
    获取模块专用的logger
    
    Args:
        module_name: 模块名称
        
    Returns:
        配置好的logger实例
    """
    # 为特定模块绑定上下文
    return logger.bind(module=module_name)

def log_function_call(func_name: str, **kwargs) -> None:
    """
    记录函数调用信息
    
    Args:
        func_name: 函数名称
        **kwargs: 函数参数
    """
    logger.debug(f"调用函数: {func_name}")
    for key, value in kwargs.items():
        logger.debug(f"  参数 {key}: {value}")

def log_file_operation(operation: str, file_path: Union[str, Path], **kwargs) -> None:
    """
    记录文件操作信息
    
    Args:
        operation: 操作类型 (read, write, delete, etc.)
        file_path: 文件路径
        **kwargs: 额外信息
    """
    logger.info(f"文件{operation}: {file_path}")
    for key, value in kwargs.items():
        logger.debug(f"  {key}: {value}")

def log_error_with_context(error: Exception, context: Optional[Dict[str, Any]] = None) -> None:
    """
    记录带上下文的错误信息
    
    Args:
        error: 异常对象
        context: 错误上下文信息
    """
    logger.error(f"发生错误: {type(error).__name__}: {str(error)}")
    if context:
        logger.error("错误上下文:")
        for key, value in context.items():
            logger.error(f"  {key}: {value}")
    
    # 记录异常栈
    logger.exception("异常详细信息:")

# 默认初始化
def init_default_logger():
    """
    使用默认配置初始化日志系统
    """
    # 从环境变量获取日志级别，默认为INFO
    log_level = os.getenv("SALARY_SYSTEM_LOG_LEVEL", "INFO")
    
    setup_logger(
        log_level=log_level,
        log_to_file=True,
        log_to_console=True
    )

# 自动初始化（当模块被导入时）
if __name__ != "__main__":
    init_default_logger()

# 对外暴露的主要接口
__all__ = [
    "setup_logger",
    "get_module_logger", 
    "log_function_call",
    "log_file_operation",
    "log_error_with_context",
    "init_default_logger",
    "logger"
]

if __name__ == "__main__":
    # 测试代码
    print("测试日志配置模块...")
    
    # 初始化日志
    setup_logger(log_level="DEBUG")
    
    # 测试各种日志级别
    logger.debug("这是DEBUG级别日志")
    logger.info("这是INFO级别日志")
    logger.warning("这是WARNING级别日志")
    logger.error("这是ERROR级别日志")
    
    # 测试模块logger
    module_logger = get_module_logger("test_module")
    module_logger.info("这是模块专用日志")
    
    # 测试文件操作日志
    log_file_operation("read", "test.xlsx", size="1.2MB")
    
    # 测试错误日志
    try:
        1 / 0
    except Exception as e:
        log_error_with_context(e, {"操作": "除法计算", "被除数": 1, "除数": 0})
    
    print("日志配置模块测试完成！") 