"""
现代化界面演示程序 - Modern UI Demo

用于测试和展示现代化界面组件的演示程序。

功能:
1. 展示现代化主窗口
2. 测试Toast消息系统
3. 演示响应式布局
4. 验证动画效果
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

try:
    from src.gui.prototype.prototype_main_window import PrototypeMainWindow as ModernMainWindow
    from src.gui.toast_system import toast_manager
    from src.gui.modern_style import MaterialDesignPalette, ModernStyleSheet
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在项目根目录运行此程序")
    sys.exit(1)


class ModernDemo(QMainWindow):
    """现代化界面演示窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("现代化界面演示 - Modern UI Demo")
        self.setMinimumSize(800, 600)
        
        # 初始化Toast系统
        toast_manager.set_parent(self)
        
        # 创建演示界面
        self._create_demo_ui()
        
        # 应用现代化样式
        self.setStyleSheet(ModernStyleSheet.get_complete_style())
    
    def _create_demo_ui(self):
        """创建演示界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)
        
        # 标题
        title = QLabel("现代化界面组件演示")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title.setFont(title_font)
        title.setStyleSheet(f"color: {MaterialDesignPalette.TEXT['primary']};")
        layout.addWidget(title)
        
        # Toast消息测试按钮
        toast_layout = QHBoxLayout()
        
        success_btn = QPushButton("成功消息")
        success_btn.clicked.connect(lambda: toast_manager.show_success("这是一个成功消息！"))
        toast_layout.addWidget(success_btn)
        
        warning_btn = QPushButton("警告消息")
        warning_btn.setProperty("buttonStyle", "warning")
        warning_btn.clicked.connect(lambda: toast_manager.show_warning("这是一个警告消息！"))
        toast_layout.addWidget(warning_btn)
        
        error_btn = QPushButton("错误消息")
        error_btn.setProperty("buttonStyle", "error")
        error_btn.clicked.connect(lambda: toast_manager.show_error("这是一个错误消息！"))
        toast_layout.addWidget(error_btn)
        
        info_btn = QPushButton("信息消息")
        info_btn.setProperty("buttonStyle", "secondary")
        info_btn.clicked.connect(lambda: toast_manager.show_info("这是一个信息消息！"))
        toast_layout.addWidget(info_btn)
        
        layout.addLayout(toast_layout)
        
        # 主窗口启动按钮
        main_window_btn = QPushButton("启动现代化主窗口")
        main_window_btn.clicked.connect(self.launch_main_window)
        layout.addWidget(main_window_btn)
        
        # 说明文本
        description = QLabel("""
        现代化界面特性:
        • Material Design配色方案
        • 响应式布局设计
        • Toast消息通知系统
        • 卡片式组件设计
        • 微动画效果
        • 阴影效果模拟
        """)
        description.setStyleSheet(f"color: {MaterialDesignPalette.TEXT['secondary']}; font-size: 10pt;")
        layout.addWidget(description)
        
        layout.addStretch()
    
    def launch_main_window(self):
        """启动现代化主窗口"""
        try:
            toast_manager.show_info("正在启动现代化主窗口...")
            
            # 延迟启动主窗口
            QTimer.singleShot(1000, self._show_main_window)
            
        except Exception as e:
            toast_manager.show_error(f"启动主窗口失败: {e}")
    
    def _show_main_window(self):
        """显示主窗口"""
        try:
            self.main_window = ModernMainWindow()
            self.main_window.show()
            toast_manager.show_success("现代化主窗口启动成功！")
        except Exception as e:
            toast_manager.show_error(f"主窗口启动失败: {e}")
            print(f"详细错误: {e}")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("现代化界面演示")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("工资异动处理系统")
    
    try:
        # 创建演示窗口
        demo = ModernDemo()
        demo.show()
        
        # 显示欢迎消息
        QTimer.singleShot(500, lambda: toast_manager.show_success("演示程序启动成功！", 3000))
        
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"程序启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main() 