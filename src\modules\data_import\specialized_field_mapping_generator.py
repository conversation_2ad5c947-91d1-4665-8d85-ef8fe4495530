#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专用字段映射生成器

为4类工资表生成专用的字段映射配置，确保Excel表头能正确映射到专用数据库字段。

创建时间: 2025-06-27
"""

from typing import Dict, List, Optional, Set
import re
from src.utils.log_config import setup_logger
from src.modules.system_config.specialized_table_templates import SpecializedTableTemplates

class SpecializedFieldMappingGenerator:
    """专用字段映射生成器"""
    
    def __init__(self):
        self.logger = setup_logger(__name__)
        self.templates = SpecializedTableTemplates()
        self._init_mapping_rules()
    
    def _init_mapping_rules(self):
        """初始化映射规则"""
        # Excel表头到数据库字段的映射规则
        self.mapping_rules = {
            # 通用字段映射
            "序号": "sequence_number",
            "人员代码": "employee_id",
            "工号": "employee_id", 
            "姓名": "employee_name",
            "部门名称": "department",
            "人员类别": "employee_type",
            "人员类别代码": "employee_type_code",
            "补发": "supplement",
            "借支": "advance",
            "应发工资": "total_salary",
            "备注": "remarks",
            
            # 离休人员专用字段
            "基本离休费": "basic_retirement_salary",
            "结余津贴": "balance_allowance",
            "生活补贴": "living_allowance",
            "住房补贴": "housing_allowance",
            "物业补贴": "property_allowance",
            "离休补贴": "retirement_allowance",
            "护理费": "nursing_fee",
            "增发一次性生活补贴": "one_time_living_allowance",
            "合计": "total",
            
            # 退休人员专用字段
            "基本退休费": "basic_retirement_salary",
            "津贴": "allowance",
            "离退休生活补贴": "retirement_living_allowance",
            "增资预付": "salary_advance",
            "2016待遇调整": "adjustment_2016",
            "2017待遇调整": "adjustment_2017",
            "2018待遇调整": "adjustment_2018",
            "2019待遇调整": "adjustment_2019",
            "2020待遇调整": "adjustment_2020",
            "2021待遇调整": "adjustment_2021",
            "2022待遇调整": "adjustment_2022",
            "2023待遇调整": "adjustment_2023",
            "公积": "provident_fund",
            "保险扣款": "insurance_deduction",
            
            # 在职人员专用字段
            "2025年岗位工资": "position_salary_2025",
            "岗位工资": "position_salary_2025",
            "2025年薪级工资": "grade_salary_2025",
            "薪级工资": "grade_salary_2025",
            "2025年基础性绩效": "basic_performance_2025",
            "基础性绩效": "basic_performance_2025",
            "卫生费": "health_fee",
            "交通补贴": "transport_allowance",
            "车补": "car_allowance",
            "通讯补贴": "communication_allowance",
            "2025年奖励性绩效预发": "performance_bonus_2025",
            "奖励性绩效预发": "performance_bonus_2025",
            "2025公积金": "provident_fund_2025",
            "代扣代存养老保险": "pension_insurance",
            
            # A岗职工专用字段
            "2025年校龄工资": "seniority_salary_2025",
            "校龄工资": "seniority_salary_2025",
            "2025年生活补贴": "living_allowance_2025"
        }
    
    def generate_mapping(self, excel_headers: List[str], template_key: str) -> Dict[str, str]:
        """
        为指定模板生成字段映射
        
        Args:
            excel_headers: Excel表头列表
            template_key: 模板键名
            
        Returns:
            字段映射字典 {数据库字段名: 显示名称}
        """
        try:
            # 获取模板字段定义
            template_fields = self.templates.get_template(template_key)
            if not template_fields:
                self.logger.warning(f"未找到模板: {template_key}")
                return {}
            
            # 创建数据库字段集合
            db_fields = {field.name for field in template_fields}
            
            # 清理Excel表头
            cleaned_headers = [self._clean_header(header) for header in excel_headers]
            
            # 生成映射
            mapping = {}
            
            # 1. 精确匹配
            for i, header in enumerate(cleaned_headers):
                if header in self.mapping_rules:
                    db_field = self.mapping_rules[header]
                    if db_field in db_fields:
                        mapping[db_field] = header
                        self.logger.debug(f"精确匹配: {header} -> {db_field}")
            
            # 2. 模糊匹配
            for i, header in enumerate(cleaned_headers):
                if header not in self.mapping_rules:
                    db_field = self._fuzzy_match(header, db_fields)
                    if db_field and db_field not in mapping:
                        mapping[db_field] = header
                        self.logger.debug(f"模糊匹配: {header} -> {db_field}")
            
            # 3. 为未映射的数据库字段生成默认显示名
            for field in template_fields:
                if field.name not in mapping and field.description:
                    mapping[field.name] = field.description
                    self.logger.debug(f"默认映射: {field.name} -> {field.description}")
            
            self.logger.info(f"为模板 {template_key} 生成了 {len(mapping)} 个字段映射")
            return mapping
            
        except Exception as e:
            self.logger.error(f"生成字段映射失败: {e}")
            return {}
    
    def _clean_header(self, header: str) -> str:
        """清理Excel表头"""
        if not isinstance(header, str):
            return str(header)
        
        # 去除换行符、制表符、多余空格
        cleaned = re.sub(r'[\n\r\t]+', '', header)
        cleaned = re.sub(r'\s+', ' ', cleaned)
        cleaned = cleaned.strip()
        
        return cleaned
    
    def _fuzzy_match(self, header: str, db_fields: Set[str]) -> Optional[str]:
        """模糊匹配字段名"""
        header_lower = header.lower()
        
        # 关键词匹配
        keyword_mapping = {
            "工资": ["salary", "wage"],
            "津贴": ["allowance"],
            "补贴": ["allowance", "subsidy"],
            "绩效": ["performance"],
            "公积金": ["provident_fund"],
            "保险": ["insurance"],
            "扣款": ["deduction"],
            "调整": ["adjustment"]
        }
        
        for chinese_keyword, english_keywords in keyword_mapping.items():
            if chinese_keyword in header:
                for db_field in db_fields:
                    for english_keyword in english_keywords:
                        if english_keyword in db_field.lower():
                            return db_field
        
        return None
    
    def generate_all_mappings(self, excel_data: Dict[str, List[str]]) -> Dict[str, Dict[str, str]]:
        """
        为所有表类型生成字段映射
        
        Args:
            excel_data: {sheet_name: headers} 格式的Excel数据
            
        Returns:
            {table_name: {db_field: display_name}} 格式的映射
        """
        all_mappings = {}
        
        for sheet_name, headers in excel_data.items():
            # 检测模板类型
            template_key = self.templates.detect_template_by_headers(headers)
            
            # 生成映射
            mapping = self.generate_mapping(headers, template_key)
            
            if mapping:
                all_mappings[sheet_name] = mapping
                self.logger.info(f"为 {sheet_name} 生成映射，使用模板: {template_key}")
        
        return all_mappings
    
    def validate_mapping(self, mapping: Dict[str, str], template_key: str) -> Dict[str, List[str]]:
        """
        验证字段映射的完整性
        
        Returns:
            验证结果 {
                "missing_fields": [...],  # 缺失的必要字段
                "extra_fields": [...],    # 多余的字段
                "warnings": [...]         # 警告信息
            }
        """
        result = {
            "missing_fields": [],
            "extra_fields": [],
            "warnings": []
        }
        
        try:
            template_fields = self.templates.get_template(template_key)
            if not template_fields:
                result["warnings"].append(f"未找到模板: {template_key}")
                return result
            
            # 必要字段检查
            required_fields = {"employee_id", "employee_name"}
            template_field_names = {field.name for field in template_fields}
            
            for required_field in required_fields:
                if required_field not in mapping:
                    result["missing_fields"].append(required_field)
            
            # 多余字段检查
            for db_field in mapping.keys():
                if db_field not in template_field_names:
                    result["extra_fields"].append(db_field)
            
            # 生成警告
            if result["missing_fields"]:
                result["warnings"].append(f"缺失必要字段: {result['missing_fields']}")
            
            if result["extra_fields"]:
                result["warnings"].append(f"包含未知字段: {result['extra_fields']}")
            
        except Exception as e:
            result["warnings"].append(f"验证过程出错: {e}")
        
        return result
