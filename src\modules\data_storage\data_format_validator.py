#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据格式验证器

本模块负责验证和修复数据格式，确保数据显示的一致性，特别是：
- 验证工资字段是否为正确的整型格式
- 检测并修复格式问题
- 提供数据格式一致性保障

主要功能：
- validate_salary_data_format(): 验证工资数据格式是否正确
- fix_format_issues(): 修复检测到的格式问题
- validate_data_consistency(): 验证数据一致性

创建时间: 2025-07-03
作者: 月度工资异动处理系统开发团队
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
import logging
import re

# 导入项目内部模块
try:
    from src.utils.log_config import setup_logger
except ImportError:
    # 如果无法导入，使用标准日志
    def setup_logger(name):
        logger = logging.getLogger(name)
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger

# 初始化日志
logger = setup_logger("data_storage.data_format_validator")


class DataFormatValidator:
    """
    数据格式验证器
    
    负责验证数据格式的正确性，检测格式问题并提供修复建议。
    特别针对工资字段的整型格式要求进行验证。
    """
    
    # 定义需要验证为整型的工资字段
    SALARY_INTEGER_FIELDS = [
        # 中文字段名（显示字段）
        '2025年岗位工资', '2025年薪级工资', '津贴', '结余津贴',
        '2025年基础性绩效', '卫生费', '交通补贴', '物业补贴',
        '通讯补贴', '2025年奖励性绩效预发', '2025公积金',
        # 英文字段名（数据库字段）- 作为备选检查
        'position_salary_2025', 'grade_salary_2025', 'allowance',
        'balance_allowance', 'basic_performance_2025', 'health_fee',
        'transport_allowance', 'property_allowance', 'communication_allowance',
        'performance_bonus_2025', 'provident_fund_2025'
    ]
    
    # 定义需要验证为浮点型的字段（保留两位小数）
    SALARY_FLOAT_FIELDS = [
        # 中文字段名（显示字段）
        '住房补贴', '车补', '补发', '借支', '应发工资', '代扣代存养老保险',
        # 英文字段名（数据库字段）- 作为备选检查
        'housing_allowance', 'car_allowance', 'supplement',
        'advance', 'total_salary', 'pension_insurance'
    ]
    
    # 定义应该为字符串的字段
    STRING_FIELDS = [
        '工号', '姓名', '部门名称', '人员类别',
        'employee_id', 'employee_code', 'name', 'department'
    ]
    
    # 定义特殊字段（月份、年份）
    SPECIAL_FIELDS = {
        'month_fields': ['month', '月份'],
        'year_fields': ['year', '年份']
    }
    
    @classmethod
    def validate_salary_data_format(cls, df: pd.DataFrame, table_name: str = "unknown", table_type: str = 'active_employees') -> Dict[str, Any]:
        """
        验证工资数据格式是否正确
        
        Args:
            df (pd.DataFrame): 要验证的数据框
            table_name (str): 表名（用于日志）
            
        Returns:
            Dict[str, Any]: 验证结果
                - is_valid: 是否通过验证
                - issues: 发现的问题列表
                - suggestions: 修复建议列表
                - field_status: 各字段的验证状态
        """
        logger.info(f"🔍 [格式验证] 开始验证表 {table_name} 的数据格式")
        
        validation_result = {
            'is_valid': True,
            'issues': [],
            'suggestions': [],
            'field_status': {},
            'summary': {
                'total_fields': 0,
                'validated_fields': 0,
                'invalid_fields': 0,
                'missing_fields': 0
            }
        }
        
        if df is None or df.empty:
            validation_result['is_valid'] = False
            validation_result['issues'].append("数据框为空或None")
            validation_result['suggestions'].append("提供有效的数据框")
            return validation_result
        
        try:
            # 获取表类型字段配置
            field_config = cls._get_table_field_config(table_type)
            
            # 1. 验证工资字段的整型格式
            cls._validate_salary_integer_fields(df, validation_result, field_config)
            
            # 2. 验证工资字段的浮点型格式
            cls._validate_salary_float_fields(df, validation_result, field_config)
            
            # 3. 验证字符串字段格式
            cls._validate_string_fields(df, validation_result, field_config)
            
            # 4. 验证特殊字段格式（月份、年份）
            cls._validate_special_fields(df, validation_result, field_config)
            
            # 5. 检查数据一致性
            cls._validate_data_consistency(df, validation_result, field_config)
            
            # 4. 统计验证结果
            cls._calculate_validation_summary(validation_result)
            
            # 5. 输出验证报告
            cls._log_validation_report(validation_result, table_name)
            
        except Exception as e:
            logger.error(f"🔍 [格式验证] 验证过程中发生错误: {e}")
            validation_result['is_valid'] = False
            validation_result['issues'].append(f"验证过程异常: {str(e)}")
            validation_result['suggestions'].append("检查数据格式和验证逻辑")
        
        return validation_result
    
    @classmethod
    def _get_table_field_config(cls, table_type: str) -> Dict[str, Any]:
        """
        获取指定表类型的字段配置
        
        Args:
            table_type (str): 表类型
            
        Returns:
            Dict[str, Any]: 字段配置
        """
        # 导入SalaryDataFormatter获取字段配置
        try:
            from .salary_data_formatter import SalaryDataFormatter
            field_mapping = SalaryDataFormatter.FIELD_TYPE_MAPPING
            return field_mapping.get(table_type, field_mapping.get('active_employees', {}))
        except ImportError:
            logger.warning(f"无法导入SalaryDataFormatter，使用默认字段配置")
            # 返回默认配置
            return {
                'id_fields': ['工号', 'employee_id'],
                'string_fields': ['姓名', '部门名称', '人员类别'],
                'integer_fields': cls.SALARY_INTEGER_FIELDS,
                'float_fields': cls.SALARY_FLOAT_FIELDS,
                'special_fields': cls.SPECIAL_FIELDS
            }
    
    @classmethod
    def _validate_salary_integer_fields(cls, df: pd.DataFrame, result: Dict[str, Any], field_config: Dict[str, Any]) -> None:
        """验证工资字段的整型格式 - 按表类型处理"""
        integer_fields = field_config.get('integer_fields', [])
        logger.debug(f"🔍 [格式验证] 验证整型字段: {integer_fields}")
        
        for field in integer_fields:
            result['summary']['total_fields'] += 1
            
            if field in df.columns:
                result['summary']['validated_fields'] += 1
                field_status = cls._check_integer_field_format(df, field)
                result['field_status'][field] = field_status
                
                if not field_status['is_valid']:
                    result['is_valid'] = False
                    result['summary']['invalid_fields'] += 1
                    result['issues'].extend(field_status['issues'])
                    result['suggestions'].extend(field_status['suggestions'])
                else:
                    logger.debug(f"🔍 [格式验证] ✅ 字段 {field} 格式正确")
            else:
                result['summary']['missing_fields'] += 1
                logger.debug(f"🔍 [格式验证] ⚠️ 字段 {field} 不存在于数据中")
    
    @classmethod
    def _check_integer_field_format(cls, df: pd.DataFrame, field: str) -> Dict[str, Any]:
        """检查单个整型字段的格式"""
        field_result = {
            'is_valid': True,
            'issues': [],
            'suggestions': [],
            'data_type': str(df[field].dtype),
            'has_decimals': False,
            'sample_values': df[field].head(3).tolist()
        }
        
        try:
            # 检查数据类型
            if df[field].dtype not in ['int64', 'int32', 'int']:
                field_result['is_valid'] = False
                field_result['issues'].append(f"字段 {field} 数据类型为 {df[field].dtype}，应为整型")
                field_result['suggestions'].append(f"将字段 {field} 转换为整型")
            
            # 检查是否包含小数点（字符串形式）
            str_series = df[field].astype(str)
            decimal_mask = str_series.str.contains(r'\.')
            decimal_count = decimal_mask.sum()
            
            if decimal_count > 0:
                field_result['is_valid'] = False
                field_result['has_decimals'] = True
                field_result['issues'].append(f"字段 {field} 包含 {decimal_count} 个小数点格式值")
                field_result['suggestions'].append(f"移除字段 {field} 的小数点显示")
                
                # 记录包含小数点的样例值
                decimal_examples = df[decimal_mask][field].head(3).tolist()
                field_result['decimal_examples'] = decimal_examples
            
            # 检查空值情况
            null_count = df[field].isnull().sum()
            if null_count > 0:
                field_result['issues'].append(f"字段 {field} 包含 {null_count} 个空值")
                field_result['suggestions'].append(f"处理字段 {field} 的空值")
            
        except Exception as e:
            field_result['is_valid'] = False
            field_result['issues'].append(f"检查字段 {field} 时发生错误: {str(e)}")
            field_result['suggestions'].append(f"检查字段 {field} 的数据完整性")
        
        return field_result
    
    @classmethod
    def _validate_salary_float_fields(cls, df: pd.DataFrame, result: Dict[str, Any], field_config: Dict[str, Any]) -> None:
        """验证工资字段的浮点型格式 - 按表类型处理"""
        float_fields = field_config.get('float_fields', [])
        logger.debug(f"🔍 [格式验证] 验证浮点型字段: {float_fields}")
        
        for field in float_fields:
            result['summary']['total_fields'] += 1
            
            if field in df.columns:
                result['summary']['validated_fields'] += 1
                field_status = cls._check_float_field_format(df, field)
                result['field_status'][field] = field_status
                
                if not field_status['is_valid']:
                    result['is_valid'] = False
                    result['summary']['invalid_fields'] += 1
                    result['issues'].extend(field_status['issues'])
                    result['suggestions'].extend(field_status['suggestions'])
                else:
                    logger.debug(f"🔍 [格式验证] ✅ 浮点型字段 {field} 格式正确")
            else:
                result['summary']['missing_fields'] += 1
                logger.debug(f"🔍 [格式验证] ⚠️ 浮点型字段 {field} 不存在于数据中")
    
    @classmethod
    def _check_float_field_format(cls, df: pd.DataFrame, field: str) -> Dict[str, Any]:
        """检查单个浮点型字段的格式"""
        field_result = {
            'is_valid': True,
            'issues': [],
            'suggestions': [],
            'data_type': str(df[field].dtype),
            'decimal_places_correct': True,
            'sample_values': df[field].head(3).tolist()
        }
        
        try:
            # 检查数据类型
            if df[field].dtype not in ['float64', 'float32', 'float']:
                field_result['is_valid'] = False
                field_result['issues'].append(f"字段 {field} 数据类型为 {df[field].dtype}，应为浮点型")
                field_result['suggestions'].append(f"将字段 {field} 转换为浮点型")
            
            # 检查小数位数是否正确（应该保留两位小数）
            decimal_issues = []
            for idx, val in enumerate(df[field].head(10)):  # 检查前10行
                if pd.notna(val):
                    # 检查是否保留了两位小数
                    decimal_str = f"{val:.2f}"
                    if f"{val}" != decimal_str and abs(val - round(val, 2)) > 1e-10:
                        decimal_issues.append(f"行{idx}: {val}")
            
            if decimal_issues:
                field_result['is_valid'] = False
                field_result['decimal_places_correct'] = False
                field_result['issues'].append(f"字段 {field} 小数位数不正确")
                field_result['suggestions'].append(f"将字段 {field} 格式化为保留两位小数")
                field_result['decimal_issues'] = decimal_issues[:3]  # 只记录前3个问题
            
            # 检查空值情况
            null_count = df[field].isnull().sum()
            if null_count > 0:
                field_result['issues'].append(f"字段 {field} 包含 {null_count} 个空值")
                field_result['suggestions'].append(f"处理字段 {field} 的空值")
            
        except Exception as e:
            field_result['is_valid'] = False
            field_result['issues'].append(f"检查浮点型字段 {field} 时发生错误: {str(e)}")
            field_result['suggestions'].append(f"检查字段 {field} 的数据完整性")
        
        return field_result
    
    @classmethod
    def _validate_string_fields(cls, df: pd.DataFrame, result: Dict[str, Any], field_config: Dict[str, Any]) -> None:
        """验证字符串字段格式 - 按表类型处理"""
        string_fields = field_config.get('string_fields', []) + field_config.get('id_fields', [])
        logger.debug(f"🔍 [格式验证] 验证字符串字段: {string_fields}")
        
        for field in string_fields:
            if field in df.columns:
                # 简单检查：确保不是数值类型的字符串字段
                if df[field].dtype in ['int64', 'float64']:
                    result['issues'].append(f"字符串字段 {field} 的数据类型为数值型: {df[field].dtype}")
                    result['suggestions'].append(f"将字段 {field} 转换为字符串类型")
    
    @classmethod
    def _validate_special_fields(cls, df: pd.DataFrame, result: Dict[str, Any], field_config: Dict[str, Any]) -> None:
        """验证特殊字段格式（月份、年份） - 按表类型处理"""
        special_fields = field_config.get('special_fields', {})
        logger.debug(f"🔍 [格式验证] 验证特殊字段: {special_fields}")
        
        # 验证月份字段
        month_fields = special_fields.get('month_fields', [])
        for field in month_fields:
            result['summary']['total_fields'] += 1
            
            if field in df.columns:
                result['summary']['validated_fields'] += 1
                field_status = cls._check_month_field_format(df, field)
                result['field_status'][field] = field_status
                
                if not field_status['is_valid']:
                    result['is_valid'] = False
                    result['summary']['invalid_fields'] += 1
                    result['issues'].extend(field_status['issues'])
                    result['suggestions'].extend(field_status['suggestions'])
                else:
                    logger.debug(f"🔍 [格式验证] ✅ 月份字段 {field} 格式正确")
            else:
                result['summary']['missing_fields'] += 1
                logger.debug(f"🔍 [格式验证] ⚠️ 月份字段 {field} 不存在于数据中")
        
        # 验证年份字段
        year_fields = special_fields.get('year_fields', [])
        for field in year_fields:
            result['summary']['total_fields'] += 1
            
            if field in df.columns:
                result['summary']['validated_fields'] += 1
                field_status = cls._check_year_field_format(df, field)
                result['field_status'][field] = field_status
                
                if not field_status['is_valid']:
                    result['is_valid'] = False
                    result['summary']['invalid_fields'] += 1
                    result['issues'].extend(field_status['issues'])
                    result['suggestions'].extend(field_status['suggestions'])
                else:
                    logger.debug(f"🔍 [格式验证] ✅ 年份字段 {field} 格式正确")
            else:
                result['summary']['missing_fields'] += 1
                logger.debug(f"🔍 [格式验证] ⚠️ 年份字段 {field} 不存在于数据中")
    
    @classmethod
    def _check_month_field_format(cls, df: pd.DataFrame, field: str) -> Dict[str, Any]:
        """检查月份字段的格式"""
        field_result = {
            'is_valid': True,
            'issues': [],
            'suggestions': [],
            'data_type': str(df[field].dtype),
            'sample_values': df[field].head(3).tolist()
        }
        
        try:
            # 检查数据类型是否为字符串
            if df[field].dtype != 'object':
                field_result['issues'].append(f"月份字段 {field} 数据类型为 {df[field].dtype}，应为字符串")
                field_result['suggestions'].append(f"将月份字段 {field} 转换为字符串类型")
            
            # 检查月份格式是否正确（两位数字，01-12）
            format_issues = []
            for idx, val in enumerate(df[field].head(10)):  # 检查前10行
                if pd.notna(val) and val != '':
                    val_str = str(val)
                    if not (val_str.isdigit() and len(val_str) == 2 and 1 <= int(val_str) <= 12):
                        format_issues.append(f"行{idx}: {val}")
            
            if format_issues:
                field_result['is_valid'] = False
                field_result['issues'].append(f"月份字段 {field} 包含无效格式值")
                field_result['suggestions'].append(f"修正月份字段 {field} 的格式为两位数字（01-12）")
                field_result['format_issues'] = format_issues[:3]  # 只记录前3个问题
            
            # 检查空值情况
            null_count = df[field].isnull().sum()
            empty_count = (df[field] == '').sum()
            if null_count > 0 or empty_count > 0:
                field_result['issues'].append(f"月份字段 {field} 包含 {null_count} 个空值和 {empty_count} 个空字符串")
                field_result['suggestions'].append(f"处理月份字段 {field} 的空值")
            
        except Exception as e:
            field_result['is_valid'] = False
            field_result['issues'].append(f"检查月份字段 {field} 时发生错误: {str(e)}")
            field_result['suggestions'].append(f"检查月份字段 {field} 的数据完整性")
        
        return field_result
    
    @classmethod
    def _check_year_field_format(cls, df: pd.DataFrame, field: str) -> Dict[str, Any]:
        """检查年份字段的格式"""
        field_result = {
            'is_valid': True,
            'issues': [],
            'suggestions': [],
            'data_type': str(df[field].dtype),
            'sample_values': df[field].head(3).tolist()
        }
        
        try:
            # 检查数据类型是否为字符串
            if df[field].dtype != 'object':
                field_result['issues'].append(f"年份字段 {field} 数据类型为 {df[field].dtype}，应为字符串")
                field_result['suggestions'].append(f"将年份字段 {field} 转换为字符串类型")
            
            # 检查年份格式是否正确（四位数字）
            format_issues = []
            for idx, val in enumerate(df[field].head(10)):  # 检查前10行
                if pd.notna(val) and val != '':
                    val_str = str(val)
                    if not (val_str.isdigit() and len(val_str) == 4 and 1900 <= int(val_str) <= 2100):
                        format_issues.append(f"行{idx}: {val}")
            
            if format_issues:
                field_result['is_valid'] = False
                field_result['issues'].append(f"年份字段 {field} 包含无效格式值")
                field_result['suggestions'].append(f"修正年份字段 {field} 的格式为四位数字年份")
                field_result['format_issues'] = format_issues[:3]  # 只记录前3个问题
            
            # 检查空值情况
            null_count = df[field].isnull().sum()
            empty_count = (df[field] == '').sum()
            if null_count > 0 or empty_count > 0:
                field_result['issues'].append(f"年份字段 {field} 包含 {null_count} 个空值和 {empty_count} 个空字符串")
                field_result['suggestions'].append(f"处理年份字段 {field} 的空值")
            
        except Exception as e:
            field_result['is_valid'] = False
            field_result['issues'].append(f"检查年份字段 {field} 时发生错误: {str(e)}")
            field_result['suggestions'].append(f"检查年份字段 {field} 的数据完整性")
        
        return field_result
    
    @classmethod
    def _validate_data_consistency(cls, df: pd.DataFrame, result: Dict[str, Any], field_config: Dict[str, Any]) -> None:
        """验证数据一致性 - 按表类型处理"""
        logger.debug("🔍 [格式验证] 验证数据一致性")
        
        # 检查是否存在重复的工号
        id_fields = field_config.get('id_fields', [])
        for id_field in id_fields:
            if id_field in df.columns:
                duplicated_count = df[id_field].duplicated().sum()
                if duplicated_count > 0:
                    result['issues'].append(f"发现 {duplicated_count} 个重复的{id_field}")
                    result['suggestions'].append(f"检查并处理重复的{id_field}记录")
                break  # 只检查一个ID字段
        
        # 检查工资字段是否有异常值（如负数）
        integer_fields = field_config.get('integer_fields', [])
        for field in integer_fields:
            if field in df.columns:
                negative_count = (df[field] < 0).sum()
                if negative_count > 0:
                    result['issues'].append(f"字段 {field} 包含 {negative_count} 个负值")
                    result['suggestions'].append(f"检查字段 {field} 的负值是否合理")
    
    @classmethod
    def _calculate_validation_summary(cls, result: Dict[str, Any]) -> None:
        """计算验证结果摘要"""
        summary = result['summary']
        if summary['validated_fields'] > 0:
            success_rate = (summary['validated_fields'] - summary['invalid_fields']) / summary['validated_fields'] * 100
            summary['success_rate'] = round(success_rate, 1)
        else:
            summary['success_rate'] = 0.0
    
    @classmethod
    def _log_validation_report(cls, result: Dict[str, Any], table_name: str) -> None:
        """输出验证报告"""
        summary = result['summary']
        logger.info(f"🔍 [格式验证] 表 {table_name} 验证完成:")
        logger.info(f"  📊 总字段数: {summary['total_fields']}")
        logger.info(f"  ✅ 已验证字段: {summary['validated_fields']}")
        logger.info(f"  ❌ 无效字段: {summary['invalid_fields']}")
        logger.info(f"  ⚠️ 缺失字段: {summary['missing_fields']}")
        logger.info(f"  📈 成功率: {summary['success_rate']}%")
        
        if result['is_valid']:
            logger.info(f"  🎉 表 {table_name} 格式验证通过")
        else:
            logger.warning(f"  ⚠️ 表 {table_name} 格式验证失败，发现 {len(result['issues'])} 个问题")
    
    @classmethod
    def fix_format_issues(cls, df: pd.DataFrame, validation_result: Optional[Dict[str, Any]] = None, table_type: str = 'active_employees') -> pd.DataFrame:
        """
        修复检测到的格式问题 - 严格按表类型处理
        
        Args:
            df (pd.DataFrame): 要修复的数据框
            validation_result (Dict[str, Any], optional): 验证结果，如果为None则先进行验证
            table_type (str): 表类型
            
        Returns:
            pd.DataFrame: 修复后的数据框
        """
        logger.info("🔧 [格式修复] 开始修复数据格式问题")
        
        if df is None or df.empty:
            logger.warning("🔧 [格式修复] 数据框为空，无需修复")
            return df
        
        try:
            # 创建数据框副本
            fixed_df = df.copy()
            
            # 获取表类型字段配置
            field_config = cls._get_table_field_config(table_type)
            
            # 如果没有提供验证结果，先进行验证
            if validation_result is None:
                validation_result = cls.validate_salary_data_format(fixed_df, table_type=table_type)
            
            # 修复工资字段格式
            cls._fix_salary_integer_fields(fixed_df, validation_result, field_config)
            
            # 修复浮点型字段格式
            cls._fix_salary_float_fields(fixed_df, validation_result, field_config)
            
            # 修复特殊字段格式（月份、年份）
            cls._fix_special_fields(fixed_df, validation_result, field_config)
            
            # 修复字符串字段格式
            cls._fix_string_fields(fixed_df, field_config)
            
            # 验证修复效果
            post_fix_validation = cls.validate_salary_data_format(fixed_df, "修复后", table_type)
            
            if post_fix_validation['is_valid']:
                logger.info("🔧 [格式修复] ✅ 数据格式修复成功")
            else:
                logger.warning(f"🔧 [格式修复] ⚠️ 修复后仍有 {len(post_fix_validation['issues'])} 个问题")
            
            return fixed_df
            
        except Exception as e:
            logger.error(f"🔧 [格式修复] 修复过程中发生错误: {e}")
            logger.exception("详细错误信息:")
            return df  # 返回原始数据
    
    @classmethod
    def _fix_salary_integer_fields(cls, df: pd.DataFrame, validation_result: Dict[str, Any], field_config: Dict[str, Any]) -> None:
        """修复工资字段的整型格式 - 按表类型处理"""
        integer_fields = field_config.get('integer_fields', [])
        logger.debug(f"🔧 [格式修复] 修复整型字段: {integer_fields}")
        
        for field in integer_fields:
            if field in df.columns:
                field_status = validation_result['field_status'].get(field)
                
                if field_status and not field_status['is_valid']:
                    logger.info(f"🔧 [格式修复] 修复字段 {field}")
                    
                    try:
                        # 转换为数值类型
                        df[field] = pd.to_numeric(df[field], errors='coerce')
                        # 填充空值
                        df[field] = df[field].fillna(0)
                        # 强制转换为整型
                        df[field] = df[field].astype(int)
                        
                        logger.info(f"🔧 [格式修复] ✅ 字段 {field} 修复完成")
                        
                    except Exception as e:
                        logger.error(f"🔧 [格式修复] ❌ 字段 {field} 修复失败: {e}")
                        # 设置默认值
                        df[field] = 0
    
    @classmethod
    def _fix_salary_float_fields(cls, df: pd.DataFrame, validation_result: Dict[str, Any], field_config: Dict[str, Any]) -> None:
        """修复工资字段的浮点型格式 - 按表类型处理"""
        float_fields = field_config.get('float_fields', [])
        logger.debug(f"🔧 [格式修复] 修复浮点型字段: {float_fields}")
        
        for field in float_fields:
            if field in df.columns:
                field_status = validation_result['field_status'].get(field)
                
                if field_status and not field_status['is_valid']:
                    logger.info(f"🔧 [格式修复] 修复浮点型字段 {field}")
                    
                    try:
                        # 转换为数值类型
                        df[field] = pd.to_numeric(df[field], errors='coerce')
                        # 填充空值
                        df[field] = df[field].fillna(0.0)
                        # 保留两位小数
                        df[field] = df[field].round(2)
                        
                        logger.info(f"🔧 [格式修复] ✅ 浮点型字段 {field} 修复完成")
                        
                    except Exception as e:
                        logger.error(f"🔧 [格式修复] ❌ 浮点型字段 {field} 修复失败: {e}")
                        # 设置默认值
                        df[field] = 0.0
    
    @classmethod
    def _fix_special_fields(cls, df: pd.DataFrame, validation_result: Dict[str, Any], field_config: Dict[str, Any]) -> None:
        """修复特殊字段格式（月份、年份） - 按表类型处理"""
        special_fields = field_config.get('special_fields', {})
        logger.debug(f"🔧 [格式修复] 修复特殊字段: {special_fields}")
        
        # 修复月份字段
        month_fields = special_fields.get('month_fields', [])
        for field in month_fields:
            if field in df.columns:
                field_status = validation_result['field_status'].get(field)
                
                if field_status and not field_status['is_valid']:
                    logger.info(f"🔧 [格式修复] 修复月份字段 {field}")
                    
                    try:
                        # 强化的月份修复逻辑
                        def fix_month_value(val):
                            if pd.isna(val) or val == '' or val is None:
                                return ''
                            
                            val_str = str(val).strip()
                            
                            # 处理各种月份格式
                            if val_str.isdigit():
                                if len(val_str) == 1:
                                    return f"0{val_str}"
                                elif len(val_str) == 2:
                                    month_int = int(val_str)
                                    if 1 <= month_int <= 12:
                                        return val_str
                                    else:
                                        return ''
                                elif len(val_str) >= 6:
                                    # 类似 202501 格式，提取后两位
                                    month_part = val_str[-2:]
                                    month_int = int(month_part)
                                    if 1 <= month_int <= 12:
                                        return month_part
                                    else:
                                        return ''
                                else:
                                    return val_str[-2:].zfill(2)
                            else:
                                # 提取数字部分
                                import re
                                match = re.search(r'(\d{1,2})', val_str)
                                if match:
                                    month_num = int(match.group(1))
                                    if 1 <= month_num <= 12:
                                        return f"{month_num:02d}"
                                return ''
                        
                        df[field] = df[field].apply(fix_month_value)
                        logger.info(f"🔧 [格式修复] ✅ 月份字段 {field} 修复完成")
                        
                    except Exception as e:
                        logger.error(f"🔧 [格式修复] ❌ 月份字段 {field} 修复失败: {e}")
                        # 设置默认值
                        df[field] = ''
        
        # 修复年份字段
        year_fields = special_fields.get('year_fields', [])
        for field in year_fields:
            if field in df.columns:
                field_status = validation_result['field_status'].get(field)
                
                if field_status and not field_status['is_valid']:
                    logger.info(f"🔧 [格式修复] 修复年份字段 {field}")
                    
                    try:
                        # 强化的年份修复逻辑
                        def fix_year_value(val):
                            if pd.isna(val) or val == '' or val is None:
                                return ''
                            
                            val_str = str(val).strip()
                            
                            # 处理数字格式
                            if val_str.replace('.', '').isdigit():
                                try:
                                    year_int = int(float(val_str))
                                    if 1900 <= year_int <= 2100:
                                        return str(year_int)
                                    else:
                                        return ''
                                except (ValueError, OverflowError):
                                    return ''
                            else:
                                # 提取年份
                                import re
                                year_match = re.search(r'(20\d{2}|19\d{2})', val_str)
                                if year_match:
                                    return year_match.group(1)
                                else:
                                    return ''
                        
                        df[field] = df[field].apply(fix_year_value)
                        logger.info(f"🔧 [格式修复] ✅ 年份字段 {field} 修复完成")
                        
                    except Exception as e:
                        logger.error(f"🔧 [格式修复] ❌ 年份字段 {field} 修复失败: {e}")
                        # 设置默认值
                        df[field] = ''
    
    @classmethod
    def _fix_string_fields(cls, df: pd.DataFrame, field_config: Dict[str, Any]) -> None:
        """修复字符串字段格式 - 按表类型处理"""
        string_fields = field_config.get('string_fields', []) + field_config.get('id_fields', [])
        logger.debug(f"🔧 [格式修复] 修复字符串字段: {string_fields}")
        
        for field in string_fields:
            if field in df.columns:
                try:
                    # 转换为字符串
                    df[field] = df[field].astype(str)
                    # 清理空值显示
                    df[field] = df[field].replace(['nan', 'None', 'NULL'], '')
                    # 去除首尾空格
                    df[field] = df[field].str.strip()
                    
                except Exception as e:
                    logger.warning(f"🔧 [格式修复] 字符串字段 {field} 修复时发生警告: {e}")
    
    @classmethod
    def validate_and_fix_if_needed(cls, df: pd.DataFrame, table_name: str = "unknown", table_type: str = 'active_employees') -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """
        验证数据格式，如果有问题则自动修复 - 严格按表类型处理
        
        Args:
            df (pd.DataFrame): 要验证和修复的数据框
            table_name (str): 表名
            table_type (str): 表类型
            
        Returns:
            Tuple[pd.DataFrame, Dict[str, Any]]: (修复后的数据框, 验证结果)
        """
        logger.info(f"🔍🔧 [一站式处理] 开始验证和修复表 {table_name}")
        
        # 第一次验证
        validation_result = cls.validate_salary_data_format(df, table_name, table_type)
        
        if validation_result['is_valid']:
            logger.info(f"🔍🔧 [一站式处理] ✅ 表 {table_name} 格式正确，无需修复")
            return df, validation_result
        else:
            logger.info(f"🔍🔧 [一站式处理] ⚠️ 表 {table_name} 格式有问题，开始自动修复")
            
            # 自动修复
            fixed_df = cls.fix_format_issues(df, validation_result, table_type)
            
            # 重新验证
            final_validation = cls.validate_salary_data_format(fixed_df, f"{table_name}_修复后", table_type)
            
            return fixed_df, final_validation


# 模块导出
__all__ = [
    "DataFormatValidator"
]


if __name__ == "__main__":
    # 测试代码
    print("测试数据格式验证器模块...")
    
    # 创建测试数据
    test_data = {
        '工号': ['10001', '10002', '10003'],
        '姓名': ['张三', '李四', '王五'],
        '2025年岗位工资': [5000.0, 4500.0, 4800.0],  # 浮点型（有问题）
        '2025年薪级工资': [3000, 2800, 3200],        # 整型（正确）
    }
    
    test_df = pd.DataFrame(test_data)
    
    # 测试验证功能
    validator = DataFormatValidator()
    result = validator.validate_salary_data_format(test_df, "测试表")
    
    print(f"验证结果: {'通过' if result['is_valid'] else '失败'}")
    print(f"发现问题: {len(result['issues'])} 个")
    
    # 测试修复功能
    if not result['is_valid']:
        fixed_df = validator.fix_format_issues(test_df, result)
        print("修复完成")