#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
格式渲染器

负责实际的数据格式化和渲染，将原始数据转换为用户友好的显示格式。
根据字段类型和格式配置，对数据进行类型转换、格式化和验证。

主要功能:
- 数据类型转换（字符串、整型、浮点型、货币等）
- 格式化渲染（千分位分隔符、小数位数、货币符号等）
- 空值处理和默认值设置
- 数据验证和错误处理
- 批量数据处理优化

支持的数据类型:
- currency: 货币格式（¥1,234.56）
- integer: 整型格式（1,234）
- float: 浮点型格式（1,234.56）
- percentage: 百分比格式（12.3%）
- date: 日期格式（2025年07月18日）
- string: 字符串格式

创建时间: 2025-07-18
作者: 统一格式管理系统重构团队
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime, date
import re
import logging
from decimal import Decimal, InvalidOperation

# 导入项目内部模块
try:
    from src.utils.log_config import setup_logger
except ImportError:
    # 如果无法导入，使用标准日志
    def setup_logger(name):
        logger = logging.getLogger(name)
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger

from .format_config import FormatConfig
from .field_registry import FieldRegistry


class FormatRenderer:
    """
    格式渲染器
    
    负责将原始数据根据配置的格式规则转换为用户友好的显示格式。
    处理各种数据类型的格式化、验证和错误处理。
    """
    
    def __init__(self, format_config: FormatConfig, field_registry: FieldRegistry):
        """
        初始化格式渲染器
        
        Args:
            format_config: 格式配置管理器
            field_registry: 字段注册系统
        """
        self.logger = setup_logger("format_management.format_renderer")
        
        self.format_config = format_config
        self.field_registry = field_registry
        
        # 格式化缓存
        self._format_cache = {}
        
        # 错误统计
        self._error_count = 0
        self._warning_count = 0
        
        self.logger.info("🎨 [格式渲染] 格式渲染器初始化完成")
    
    # ================== 主要渲染接口 ==================
    
    def render_dataframe(self, df: pd.DataFrame, table_type: str) -> pd.DataFrame:
        """
        渲染整个DataFrame
        
        Args:
            df: 原始DataFrame
            table_type: 表格类型
            
        Returns:
            格式化后的DataFrame
        """
        try:
            if df.empty:
                return df
            
            # 创建副本以避免修改原始数据
            formatted_df = df.copy()
            
            # 获取字段类型映射
            field_types = self.field_registry.get_table_field_types(table_type)
            
            # 逐列格式化
            for column in formatted_df.columns:
                field_type = field_types.get(column, 'string')
                
                try:
                    formatted_df[column] = self.render_column(
                        formatted_df[column], 
                        field_type, 
                        column, 
                        table_type
                    )
                except Exception as e:
                    self.logger.error(f"🎨 [格式渲染] 列格式化失败 {column}: {e}")
                    self._error_count += 1
                    # 保留原始数据
                    continue
            
            self.logger.info(f"🎨 [格式渲染] DataFrame格式化完成: {table_type}, 行数: {len(formatted_df)}, 列数: {len(formatted_df.columns)}")
            return formatted_df
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] DataFrame格式化失败: {e}")
            return df
    
    def render_column(self, 
                     column: pd.Series, 
                     field_type: str, 
                     field_name: str, 
                     table_type: str) -> pd.Series:
        """
        渲染单个列
        
        Args:
            column: 原始列数据
            field_type: 字段类型
            field_name: 字段名
            table_type: 表格类型
            
        Returns:
            格式化后的列数据
        """
        try:
            # 获取格式配置
            format_config = self.format_config.get_format_rules(field_type)
            if not format_config:
                self.logger.warning(f"🎨 [格式渲染] 未找到格式配置: {field_type}")
                return column
            
            # 根据字段类型选择渲染方法
            if field_type == 'currency':
                return self._render_currency_column(column, format_config, field_name)
            elif field_type == 'integer':
                return self._render_integer_column(column, format_config, field_name)
            elif field_type == 'float':
                return self._render_float_column(column, format_config, field_name)
            elif field_type == 'percentage':
                return self._render_percentage_column(column, format_config, field_name)
            elif field_type == 'date':
                return self._render_date_column(column, format_config, field_name)
            elif field_type == 'string':
                return self._render_string_column(column, format_config, field_name)
            else:
                self.logger.warning(f"🎨 [格式渲染] 未知字段类型: {field_type}")
                return column
                
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 列格式化失败 {field_name}: {e}")
            return column
    
    def render_value(self, 
                    value: Any, 
                    field_type: str, 
                    field_name: str = None) -> str:
        """
        渲染单个值
        
        Args:
            value: 原始值
            field_type: 字段类型
            field_name: 字段名（可选）
            
        Returns:
            格式化后的字符串
        """
        try:
            # 获取格式配置
            format_config = self.format_config.get_format_rules(field_type)
            if not format_config:
                return str(value) if value is not None else ""
            
            # 根据字段类型选择渲染方法
            if field_type == 'currency':
                return self._render_currency_value(value, format_config)
            elif field_type == 'integer':
                return self._render_integer_value(value, format_config)
            elif field_type == 'float':
                return self._render_float_value(value, format_config)
            elif field_type == 'percentage':
                return self._render_percentage_value(value, format_config)
            elif field_type == 'date':
                return self._render_date_value(value, format_config)
            elif field_type == 'string':
                return self._render_string_value(value, format_config)
            else:
                return str(value) if value is not None else ""
                
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 值格式化失败 {field_name}: {e}")
            return str(value) if value is not None else ""
    
    # ================== 货币类型渲染 ==================
    
    def _render_currency_column(self, 
                               column: pd.Series, 
                               format_config: Dict, 
                               field_name: str) -> pd.Series:
        """渲染货币类型列"""
        try:
            def format_currency(value):
                return self._render_currency_value(value, format_config)
            
            return column.apply(format_currency)
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 货币列格式化失败 {field_name}: {e}")
            return column
    
    def _render_currency_value(self, value: Any, format_config: Dict) -> str:
        """渲染货币值"""
        try:
            # 处理空值
            if pd.isna(value) or value is None:
                return format_config.get('zero_display', '0.00')
            
            # 转换为数值
            try:
                if isinstance(value, str):
                    # 清理字符串中的非数字字符
                    cleaned_value = re.sub(r'[^\d.-]', '', str(value))
                    if not cleaned_value:
                        return format_config.get('zero_display', '0.00')
                    numeric_value = float(cleaned_value)
                else:
                    numeric_value = float(value)
            except (ValueError, TypeError):
                return format_config.get('zero_display', '0.00')
            
            # 格式化设置
            decimal_places = format_config.get('decimal_places', 2)
            thousand_separator = format_config.get('thousand_separator', ',')
            symbol = format_config.get('symbol', '¥')
            symbol_position = format_config.get('symbol_position', 'prefix')
            negative_format = format_config.get('negative_format', '-{symbol}{value}')
            
            # 格式化数值
            if numeric_value == 0:
                return format_config.get('zero_display', '0.00')
            
            # 格式化为指定小数位数
            formatted_value = f"{abs(numeric_value):,.{decimal_places}f}"
            
            # 替换千分位分隔符
            if thousand_separator != ',':
                formatted_value = formatted_value.replace(',', thousand_separator)
            
            # 添加货币符号
            if symbol_position == 'prefix':
                result = f"{symbol}{formatted_value}"
            else:
                result = f"{formatted_value}{symbol}"
            
            # 处理负数
            if numeric_value < 0:
                result = negative_format.format(symbol=symbol, value=formatted_value)
            
            return result
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 货币值格式化失败: {e}")
            return str(value) if value is not None else ""
    
    # ================== 整型渲染 ==================
    
    def _render_integer_column(self, 
                              column: pd.Series, 
                              format_config: Dict, 
                              field_name: str) -> pd.Series:
        """渲染整型列"""
        try:
            def format_integer(value):
                return self._render_integer_value(value, format_config)
            
            return column.apply(format_integer)
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 整型列格式化失败 {field_name}: {e}")
            return column
    
    def _render_integer_value(self, value: Any, format_config: Dict) -> str:
        """渲染整型值"""
        try:
            # 处理空值
            if pd.isna(value) or value is None:
                return format_config.get('zero_display', '0')
            
            # 转换为整数
            try:
                if isinstance(value, str):
                    cleaned_value = re.sub(r'[^\d.-]', '', str(value))
                    if not cleaned_value:
                        return format_config.get('zero_display', '0')
                    numeric_value = int(float(cleaned_value))
                else:
                    numeric_value = int(float(value))
            except (ValueError, TypeError):
                return format_config.get('zero_display', '0')
            
            # 格式化设置
            thousand_separator = format_config.get('thousand_separator', ',')
            
            # 格式化数值
            if numeric_value == 0:
                return format_config.get('zero_display', '0')
            
            # 添加千分位分隔符
            formatted_value = f"{numeric_value:,}"
            
            # 替换千分位分隔符
            if thousand_separator != ',':
                formatted_value = formatted_value.replace(',', thousand_separator)
            
            return formatted_value
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 整型值格式化失败: {e}")
            return str(value) if value is not None else ""
    
    # ================== 浮点型渲染 ==================
    
    def _render_float_column(self, 
                            column: pd.Series, 
                            format_config: Dict, 
                            field_name: str) -> pd.Series:
        """渲染浮点型列"""
        try:
            def format_float(value):
                return self._render_float_value(value, format_config)
            
            return column.apply(format_float)
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 浮点型列格式化失败 {field_name}: {e}")
            return column
    
    def _render_float_value(self, value: Any, format_config: Dict) -> str:
        """渲染浮点型值"""
        try:
            # 处理空值
            if pd.isna(value) or value is None:
                return format_config.get('zero_display', '0.00')
            
            # 转换为浮点数
            try:
                if isinstance(value, str):
                    cleaned_value = re.sub(r'[^\d.-]', '', str(value))
                    if not cleaned_value:
                        return format_config.get('zero_display', '0.00')
                    numeric_value = float(cleaned_value)
                else:
                    numeric_value = float(value)
            except (ValueError, TypeError):
                return format_config.get('zero_display', '0.00')
            
            # 格式化设置
            decimal_places = format_config.get('decimal_places', 2)
            thousand_separator = format_config.get('thousand_separator', ',')
            
            # 格式化数值
            if numeric_value == 0:
                return format_config.get('zero_display', '0.00')
            
            # 格式化为指定小数位数
            formatted_value = f"{numeric_value:,.{decimal_places}f}"
            
            # 替换千分位分隔符
            if thousand_separator != ',':
                formatted_value = formatted_value.replace(',', thousand_separator)
            
            return formatted_value
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 浮点型值格式化失败: {e}")
            return str(value) if value is not None else ""
    
    # ================== 百分比渲染 ==================
    
    def _render_percentage_column(self, 
                                 column: pd.Series, 
                                 format_config: Dict, 
                                 field_name: str) -> pd.Series:
        """渲染百分比列"""
        try:
            def format_percentage(value):
                return self._render_percentage_value(value, format_config)
            
            return column.apply(format_percentage)
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 百分比列格式化失败 {field_name}: {e}")
            return column
    
    def _render_percentage_value(self, value: Any, format_config: Dict) -> str:
        """渲染百分比值"""
        try:
            # 处理空值
            if pd.isna(value) or value is None:
                return "0%"
            
            # 转换为数值
            try:
                if isinstance(value, str):
                    cleaned_value = re.sub(r'[^\d.-]', '', str(value))
                    if not cleaned_value:
                        return "0%"
                    numeric_value = float(cleaned_value)
                else:
                    numeric_value = float(value)
            except (ValueError, TypeError):
                return "0%"
            
            # 格式化设置
            decimal_places = format_config.get('decimal_places', 1)
            symbol = format_config.get('symbol', '%')
            multiply_by_100 = format_config.get('multiply_by_100', True)
            
            # 计算百分比值
            if multiply_by_100:
                percentage_value = numeric_value * 100
            else:
                percentage_value = numeric_value
            
            # 格式化
            formatted_value = f"{percentage_value:.{decimal_places}f}{symbol}"
            
            return formatted_value
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 百分比值格式化失败: {e}")
            return str(value) if value is not None else ""
    
    # ================== 日期渲染 ==================
    
    def _render_date_column(self, 
                           column: pd.Series, 
                           format_config: Dict, 
                           field_name: str) -> pd.Series:
        """渲染日期列"""
        try:
            def format_date(value):
                return self._render_date_value(value, format_config)
            
            return column.apply(format_date)
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 日期列格式化失败 {field_name}: {e}")
            return column
    
    def _render_date_value(self, value: Any, format_config: Dict) -> str:
        """渲染日期值"""
        try:
            # 处理空值
            if pd.isna(value) or value is None:
                return ""
            
            # 转换为日期
            try:
                if isinstance(value, str):
                    # 尝试解析字符串日期
                    input_format = format_config.get('input_format', '%Y-%m-%d')
                    date_obj = datetime.strptime(value, input_format)
                elif isinstance(value, (datetime, date)):
                    date_obj = value
                else:
                    return str(value)
            except (ValueError, TypeError):
                return str(value)
            
            # 格式化设置
            display_format = format_config.get('display_format', '%Y年%m月%d日')
            
            # 格式化日期
            formatted_date = date_obj.strftime(display_format)
            
            return formatted_date
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 日期值格式化失败: {e}")
            return str(value) if value is not None else ""
    
    # ================== 字符串渲染 ==================
    
    def _render_string_column(self, 
                             column: pd.Series, 
                             format_config: Dict, 
                             field_name: str) -> pd.Series:
        """渲染字符串列"""
        try:
            def format_string(value):
                return self._render_string_value(value, format_config)
            
            return column.apply(format_string)
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 字符串列格式化失败 {field_name}: {e}")
            return column
    
    def _render_string_value(self, value: Any, format_config: Dict) -> str:
        """渲染字符串值"""
        try:
            # 处理空值
            if pd.isna(value) or value is None:
                return format_config.get('empty_display', '')
            
            # 转换为字符串
            str_value = str(value)
            
            # 格式化设置
            trim_whitespace = format_config.get('trim_whitespace', True)
            max_length = format_config.get('max_length', 100)
            
            # 处理空白字符
            if trim_whitespace:
                str_value = str_value.strip()
            
            # 处理长度限制
            if len(str_value) > max_length:
                str_value = str_value[:max_length-3] + "..."
            
            # 如果处理后为空，返回空值显示
            if not str_value:
                return format_config.get('empty_display', '')
            
            return str_value
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 字符串值格式化失败: {e}")
            return str(value) if value is not None else ""
    
    # ================== 工具和状态方法 ==================
    
    def clear_cache(self):
        """清除格式化缓存"""
        try:
            self._format_cache.clear()
            self.logger.debug("🎨 [格式渲染] 缓存已清除")
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 清除缓存失败: {e}")
    
    def get_rendering_statistics(self) -> Dict[str, Any]:
        """获取渲染统计信息"""
        try:
            return {
                'error_count': self._error_count,
                'warning_count': self._warning_count,
                'cache_size': len(self._format_cache),
                'supported_types': [
                    'currency', 'integer', 'float', 'percentage', 'date', 'string'
                ]
            }
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 获取统计信息失败: {e}")
            return {"error": str(e)}
    
    def validate_data_types(self, df: pd.DataFrame, table_type: str) -> Dict[str, List[str]]:
        """
        验证数据类型
        
        Args:
            df: 数据框
            table_type: 表格类型
            
        Returns:
            验证结果字典 {status: [messages]}
        """
        try:
            result = {
                'valid': [],
                'warnings': [],
                'errors': []
            }
            
            field_types = self.field_registry.get_table_field_types(table_type)
            
            for column in df.columns:
                if column not in field_types:
                    result['warnings'].append(f"未定义字段类型: {column}")
                    continue
                
                field_type = field_types[column]
                
                # 验证数据类型一致性
                if field_type in ['currency', 'integer', 'float']:
                    non_numeric_count = 0
                    for value in df[column].dropna():
                        try:
                            float(str(value).replace(',', '').replace('¥', ''))
                        except (ValueError, TypeError):
                            non_numeric_count += 1
                    
                    if non_numeric_count > 0:
                        result['warnings'].append(
                            f"字段 {column} 包含 {non_numeric_count} 个非数值数据"
                        )
                    else:
                        result['valid'].append(f"字段 {column} 类型验证通过")
                else:
                    result['valid'].append(f"字段 {column} 类型验证通过")
            
            return result
            
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 数据类型验证失败: {e}")
            return {"errors": [str(e)]}
    
    def reset_statistics(self):
        """重置统计信息"""
        try:
            self._error_count = 0
            self._warning_count = 0
            self.logger.debug("🎨 [格式渲染] 统计信息已重置")
        except Exception as e:
            self.logger.error(f"🎨 [格式渲染] 重置统计信息失败: {e}")