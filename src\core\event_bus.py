"""
事件总线系统 - 架构重构核心组件

解决组件间耦合度过高的问题，实现基于事件的松耦合架构。
提供发布-订阅模式的事件通信机制。

主要功能：
1. 事件发布和订阅
2. 异步事件处理
3. 事件路由和过滤
4. 错误处理和监控

架构目标：
- 消除组件间直接依赖
- 提高系统可扩展性
- 实现模块化架构
"""

from typing import Dict, List, Callable, Any, Optional, Type
from dataclasses import dataclass, field
from abc import ABC, abstractmethod
from enum import Enum
import threading
import asyncio
import logging
from datetime import datetime
import uuid
from concurrent.futures import ThreadPoolExecutor
import traceback

from src.utils.log_config import setup_logger


class EventPriority(Enum):
    """事件优先级"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class Event:
    """基础事件类"""
    event_type: str
    data: Dict[str, Any] = field(default_factory=dict)
    source: Optional[str] = None
    target: Optional[str] = None
    priority: EventPriority = EventPriority.NORMAL
    timestamp: datetime = field(default_factory=datetime.now)
    event_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    correlation_id: Optional[str] = None  # 用于关联相关事件
    
    def get(self, key: str, default=None):
        """提供类似字典的get方法，用于向后兼容"""
        return getattr(self, key, default)


# 具体事件类型定义
@dataclass
class SortRequestEvent(Event):
    """排序请求事件"""
    table_name: str = ""
    sort_columns: List[Dict[str, Any]] = field(default_factory=list)
    current_page: int = 1
    
    def __post_init__(self):
        self.event_type = "sort_request"


@dataclass
class PageRequestEvent(Event):
    """分页请求事件"""
    table_name: str = ""
    page: int = 1
    page_size: int = 50
    preserve_sort: bool = True
    
    def __post_init__(self):
        self.event_type = "page_request"


@dataclass
class DataUpdatedEvent(Event):
    """数据更新事件"""
    table_name: str = ""
    data: Any = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        self.event_type = "data_updated"


@dataclass
class FieldMappingChangeEvent(Event):
    """字段映射变更事件"""
    table_name: str = ""
    old_mapping: Dict[str, str] = field(default_factory=dict)
    new_mapping: Dict[str, str] = field(default_factory=dict)
    
    def __post_init__(self):
        self.event_type = "field_mapping_change"


@dataclass
class NavigationChangeEvent(Event):
    """导航变更事件"""
    old_path: str = ""
    new_path: str = ""
    context: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        self.event_type = "navigation_change"


@dataclass
class ErrorEvent(Event):
    """错误事件"""
    error_type: str = ""
    error_message: str = ""
    error_details: Dict[str, Any] = field(default_factory=dict)
    stack_trace: Optional[str] = None
    
    def __post_init__(self):
        self.event_type = "error"
        self.priority = EventPriority.HIGH


class EventFilter:
    """事件过滤器"""
    
    def __init__(self, 
                 event_types: Optional[List[str]] = None,
                 sources: Optional[List[str]] = None,
                 targets: Optional[List[str]] = None,
                 min_priority: EventPriority = EventPriority.LOW):
        self.event_types = event_types or []
        self.sources = sources or []
        self.targets = targets or []
        self.min_priority = min_priority
    
    def should_process(self, event: Event) -> bool:
        """判断是否应该处理此事件"""
        # 检查事件类型
        if self.event_types and event.event_type not in self.event_types:
            return False
        
        # 检查源
        if self.sources and event.source not in self.sources:
            return False
        
        # 检查目标
        if self.targets and event.target not in self.targets:
            return False
        
        # 检查优先级
        if event.priority.value < self.min_priority.value:
            return False
        
        return True


class EventHandler:
    """事件处理器"""
    
    def __init__(self, 
                 handler_func: Callable[[Event], Any],
                 event_filter: Optional[EventFilter] = None,
                 async_handler: bool = False,
                 max_retries: int = 0):
        self.handler_func = handler_func
        self.event_filter = event_filter or EventFilter()
        self.async_handler = async_handler
        self.max_retries = max_retries
        self.handler_id = str(uuid.uuid4())
        self.created_at = datetime.now()
        self.call_count = 0
        self.error_count = 0
        self.last_called = None
        self.last_error = None
    
    def can_handle(self, event: Event) -> bool:
        """判断是否可以处理此事件"""
        return self.event_filter.should_process(event)
    
    def handle(self, event: Event) -> Any:
        """处理事件"""
        self.call_count += 1
        self.last_called = datetime.now()
        
        try:
            return self.handler_func(event)
        except Exception as e:
            self.error_count += 1
            self.last_error = str(e)
            raise


class EventBus:
    """事件总线，实现组件解耦"""
    
    def __init__(self, max_workers: int = 10):
        self.logger = setup_logger("EventBus")
        
        # 事件处理器注册表
        self._handlers: Dict[str, List[EventHandler]] = {}
        
        # 线程池用于异步处理
        self._thread_pool = ThreadPoolExecutor(max_workers=max_workers)
        
        # 事件队列
        self._event_queue: List[Event] = []
        self._queue_lock = threading.Lock()
        
        # 事件历史
        self._event_history: List[Event] = []
        self._max_history = 1000
        
        # 统计信息
        self._published_count = 0
        self._processed_count = 0
        self._error_count = 0
        
        # 线程安全
        self._lock = threading.RLock()
        
        self.logger.info("事件总线初始化完成")
    
    def subscribe(self, 
                  event_type: str, 
                  handler_func: Callable[[Event], Any],
                  event_filter: Optional[EventFilter] = None,
                  async_handler: bool = False,
                  max_retries: int = 0) -> str:
        """
        订阅事件
        
        Args:
            event_type: 事件类型
            handler_func: 处理函数
            event_filter: 事件过滤器
            async_handler: 是否异步处理
            max_retries: 最大重试次数
            
        Returns:
            str: 处理器ID
        """
        with self._lock:
            if event_type not in self._handlers:
                self._handlers[event_type] = []
            
            handler = EventHandler(
                handler_func=handler_func,
                event_filter=event_filter,
                async_handler=async_handler,
                max_retries=max_retries
            )
            
            self._handlers[event_type].append(handler)
            
            self.logger.debug(f"已订阅事件: {event_type}, 处理器ID: {handler.handler_id}")
            return handler.handler_id
    
    def unsubscribe(self, event_type: str, handler_id: str) -> bool:
        """
        取消订阅
        
        Args:
            event_type: 事件类型
            handler_id: 处理器ID
            
        Returns:
            bool: 是否成功取消订阅
        """
        with self._lock:
            if event_type not in self._handlers:
                return False
            
            handlers = self._handlers[event_type]
            for i, handler in enumerate(handlers):
                if handler.handler_id == handler_id:
                    del handlers[i]
                    self.logger.debug(f"已取消订阅: {event_type}, 处理器ID: {handler_id}")
                    return True
            
            return False
    
    def publish(self, event: Event, wait_for_completion: bool = False) -> bool:
        """
        发布事件 - 新架构标准
        
        Args:
            event: 要发布的事件对象
            wait_for_completion: 是否等待处理完成
            
        Returns:
            bool: 是否成功发布
        """
        try:
            with self._lock:
                self._published_count += 1
                
                # 添加到历史记录
                self._add_to_history(event)
                
                # 获取对应的处理器
                handlers = self._handlers.get(event.event_type, [])
                
                if not handlers:
                    self.logger.debug(f"事件无处理器: {event.event_type}")
                    return True
                
                # 过滤可以处理此事件的处理器
                applicable_handlers = [h for h in handlers if h.can_handle(event)]
                
                if not applicable_handlers:
                    self.logger.debug(f"事件无适用处理器: {event.event_type}")
                    return True
                
                self.logger.debug(f"发布事件: {event.event_type}, {len(applicable_handlers)}个处理器")
                
                # 处理事件
                if wait_for_completion:
                    return self._process_event_sync(event, applicable_handlers)
                else:
                    return self._process_event_async(event, applicable_handlers)
                    
        except Exception as e:
            self.logger.error(f"发布事件失败: {e}", exc_info=True)
            self._error_count += 1
            return False
    
    def _process_event_sync(self, event: Event, handlers: List[EventHandler]) -> bool:
        """同步处理事件"""
        success = True
        
        for handler in handlers:
            try:
                if handler.async_handler:
                    # 异步处理器在同步模式下等待完成
                    future = self._thread_pool.submit(handler.handle, event)
                    future.result(timeout=30)  # 30秒超时
                else:
                    handler.handle(event)
                
                self._processed_count += 1
                
            except Exception as e:
                self.logger.error(f"事件处理失败: {handler.handler_id}, {e}")
                self._error_count += 1
                
                # 发布错误事件
                error_event = ErrorEvent(
                    error_type="event_handler_error",
                    error_message=str(e),
                    error_details={
                        "handler_id": handler.handler_id,
                        "original_event": event.event_id,
                        "event_type": event.event_type
                    },
                    stack_trace=traceback.format_exc()
                )
                # 避免递归错误，直接记录日志
                self.logger.error(f"处理器错误事件: {error_event.error_message}")
        
        return success
    
    def _process_event_async(self, event: Event, handlers: List[EventHandler]) -> bool:
        """异步处理事件"""
        try:
            for handler in handlers:
                if handler.async_handler:
                    # 提交到线程池
                    future = self._thread_pool.submit(self._handle_with_retry, handler, event)
                    future.add_done_callback(lambda f: self._on_async_complete(f, handler, event))
                else:
                    # 同步处理器直接处理
                    try:
                        handler.handle(event)
                        self._processed_count += 1
                    except Exception as e:
                        self.logger.error(f"同步事件处理失败: {handler.handler_id}, {e}")
                        self._error_count += 1
            
            return True
            
        except Exception as e:
            self.logger.error(f"异步事件处理失败: {e}")
            self._error_count += 1
            return False
    
    def _handle_with_retry(self, handler: EventHandler, event: Event) -> Any:
        """带重试的事件处理"""
        last_exception = None
        
        for attempt in range(handler.max_retries + 1):
            try:
                return handler.handle(event)
            except Exception as e:
                last_exception = e
                if attempt < handler.max_retries:
                    self.logger.warning(f"事件处理失败，重试 {attempt + 1}/{handler.max_retries}: {e}")
                else:
                    self.logger.error(f"事件处理最终失败: {e}")
        
        raise last_exception
    
    def _on_async_complete(self, future, handler: EventHandler, event: Event):
        """异步处理完成回调"""
        try:
            future.result()
            self._processed_count += 1
        except Exception as e:
            self.logger.error(f"异步事件处理失败: {handler.handler_id}, {e}")
            self._error_count += 1
    
    def _add_to_history(self, event: Event):
        """添加事件到历史记录"""
        try:
            self._event_history.append(event)
            
            # 限制历史记录数量
            if len(self._event_history) > self._max_history:
                self._event_history = self._event_history[-self._max_history:]
                
        except Exception as e:
            self.logger.error(f"添加事件历史失败: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取事件总线统计信息"""
        with self._lock:
            handler_stats = {}
            for event_type, handlers in self._handlers.items():
                handler_stats[event_type] = {
                    "handler_count": len(handlers),
                    "total_calls": sum(h.call_count for h in handlers),
                    "total_errors": sum(h.error_count for h in handlers)
                }
            
            return {
                "published_events": self._published_count,
                "processed_events": self._processed_count,
                "error_count": self._error_count,
                "event_types": list(self._handlers.keys()),
                "handler_statistics": handler_stats,
                "queue_size": len(self._event_queue),
                "history_size": len(self._event_history)
            }
    
    def get_event_history(self, event_type: Optional[str] = None, limit: int = 50) -> List[Event]:
        """获取事件历史"""
        with self._lock:
            if event_type:
                filtered_history = [e for e in self._event_history if e.event_type == event_type]
            else:
                filtered_history = self._event_history
            
            return filtered_history[-limit:]
    
    def clear_history(self):
        """清除事件历史"""
        with self._lock:
            self._event_history.clear()
            self.logger.info("事件历史已清除")
    
    def shutdown(self):
        """关闭事件总线"""
        try:
            self._thread_pool.shutdown(wait=True)
            self.logger.info("事件总线已关闭")
        except Exception as e:
            self.logger.error(f"关闭事件总线失败: {e}")


# 全局事件总线实例
_global_event_bus: Optional[EventBus] = None
_bus_lock = threading.Lock()


def get_event_bus() -> EventBus:
    """获取全局事件总线实例（单例模式）"""
    global _global_event_bus
    
    if _global_event_bus is None:
        with _bus_lock:
            if _global_event_bus is None:
                _global_event_bus = EventBus()
    
    return _global_event_bus 